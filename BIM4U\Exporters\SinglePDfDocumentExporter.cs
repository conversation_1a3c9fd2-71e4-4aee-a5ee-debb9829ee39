﻿using BIM4U.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ceTe.DynamicPDF.HtmlConverter;
using static DevExpress.XtraPrinting.Native.ExportOptionsPropertiesNames;
using DevExpress.XtraRichEdit.Model;
using System.IO;



namespace BIM4U.Exporters
{
    public class SinglePDfDocumentExporter
    {
        #region EventsAndDelegates
        public event OnErrorDelegate OnError;
        #endregion

        public async Task<bool> ReadDataAndExportSinglePDFDocumentAsync(ExcelData data, string exportPdfPath)
        {
            try
            {
                await HtmlToPDFExporterTemplateAsync(
                    data.CompanyName,
                    data.CompanyLogoUrl,
                    data.ProductName,
                    data.ProductCode,
                    data.ProductSpecification,
                    data.ProductDescription,
                    data.ProductImageUrl,
                    data.ProductTechnicalImageUrl,
                    data.ProductImagesUrl,
                    data.CatalogueUrl,
                    data.Colour1CategoryTitle,
                    data.Colour2CategoryTitle,
                    data.Colour3CategoryTitle,
                    data.Colour1Image,
                    data.Colour2Image,
                    data.Colour3Image,
                    data.ProductImageColumnHeader,
                    data.TechnicalDrawingColumnHeader,
                    exportPdfPath);

                return true;
            }
            catch (Exception ex)
            {
                OnError?.Invoke(data.RowIndex, ex.Message);
                return false;
            }
        }

        private async Task HtmlToPDFExporterTemplateAsync(
            string company,
            string companyLogo,
            string productName,
            string productCode,
            string productSpecification,
            string productDescription,
            string productImageUrl,
            string productTechnicalImageUrl,
            List<string> productImagesUrl,
            string catalogue,
            string colour1CategoryTitle,
            string colour2CategoryTitle,
            string colour3CategoryTitle,
            string colour1Image,
            string colour2Image,
            string colour3Image,
            string productImageColumnHeader,
            string technicalDrawingColumnHeader,
            string path)
        {
            try
            {
                string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "PDFLayout", "template.html");
                string htmlTemplate = File.ReadAllText(templatePath);

                string htmlExtender = "";
                foreach (string imageUrl in productImagesUrl)
                {
                    htmlExtender += "<img class=\"img-fluid\" src=\"" + EncodeTheImage(imageUrl) + "\" alt=\"Additional Image\" />\r\n";
                }

                string companyLogoImg = !string.IsNullOrEmpty(companyLogo)
                    ? "<img src=\"" + EncodeTheImage(companyLogo) + "\" alt=\"Supplier Logo\" id=\"headerLogo\" />"
                    : "";

                string productImg = !string.IsNullOrEmpty(productImageUrl)
                    ? "<img src=\"" + EncodeTheImage(productImageUrl) + "\" class=\"img-fluid d-block\" />"
                    : "";

                string technicalImg = !string.IsNullOrEmpty(productTechnicalImageUrl)
                    ? "<img src=\"" + EncodeTheImage(productTechnicalImageUrl) + "\" class=\"img-fluid d-block\" />"
                    : "";

                string footerLogoImg = "<img src=\"" + EncodeTheImage("images/bim4u.jpg") + "\" alt=\"BIM4U Logo\" id=\"footerLogo\" style=\"height:100px; max-width:100%; object-fit:contain; display:block; margin-left:auto;\">";

                productCode = string.IsNullOrEmpty(productCode) ? "N/A" : productCode;

                // Handle colour images and category titles
                string colour1CategoryTitleText = string.IsNullOrEmpty(colour1CategoryTitle) ? "ALUMINIUM COLOURS" : colour1CategoryTitle;
                string colour2CategoryTitleText = string.IsNullOrEmpty(colour2CategoryTitle) ? "NON SLIP INSERT COLOURS" : colour2CategoryTitle;

                string colour1ImageHtml = !string.IsNullOrEmpty(colour1Image)
                    ? "<img src=\"" + EncodeTheImage(colour1Image) + "\" alt=\"Colour 1\" class=\"colour-image\" />"
                    : "<img src=\"images/placeholder-product.jpg\" alt=\"Colour 1\" class=\"colour-image\" />";

                string colour2ImageHtml = !string.IsNullOrEmpty(colour2Image)
                    ? "<img src=\"" + EncodeTheImage(colour2Image) + "\" alt=\"Colour 2\" class=\"colour-image\" />"
                    : "<img src=\"images/placeholder-product.jpg\" alt=\"Colour 2\" class=\"colour-image\" />";

                // Generate dynamic image section title
                string imageSectionTitle = $"{productImageColumnHeader ?? "Product Image"} and {technicalDrawingColumnHeader ?? "Technical Drawing"}";

                string finalHtml = htmlTemplate
                    .Replace("{{CompanyLogo}}", companyLogoImg)
                    .Replace("{{ProductName}}", productName)
                    .Replace("{{ProductCode}}", productCode)
                    .Replace("{{ProductDescription}}", productDescription)
                    .Replace("{{ProductSpecification}}", productSpecification)
                    .Replace("{{CompanyName}}", company)
                    .Replace("{{ProductImage}}", productImg)
                    .Replace("{{TechnicalDrawing}}", technicalImg)
                    .Replace("{{ExtraImages}}", htmlExtender)
                    .Replace("{{FooterLogo}}", footerLogoImg)
                    .Replace("{{Colour1CategoryTitle}}", colour1CategoryTitleText)
                    .Replace("{{Colour2CategoryTitle}}", colour2CategoryTitleText)
                    .Replace("{{Colour1Image}}", colour1ImageHtml)
                    .Replace("{{Colour2Image}}", colour2ImageHtml)
                    .Replace("{{ImageSectionTitle}}", imageSectionTitle);

                ConversionOptions options = new ConversionOptions(PageSize.A4, PageOrientation.Portrait);

                await Converter.ConvertAsync(finalHtml, path, null, options);
            }
            catch (Exception ex)
            {
                throw new Exception("Failed to generate PDF: " + ex.Message, ex);
            }
        }

        private string EncodeTheImage(string filePath)
        {
            try
            {
                byte[] imageBytes = File.ReadAllBytes(filePath);
                return "data:image/jpeg;base64," + Convert.ToBase64String(imageBytes);
            }
            catch
            {
                return "";
            }
        }
    }
}


