﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <RootNamespace>BIM4U</RootNamespace>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <AssemblyName>bim4uimporter</AssemblyName>
    <PackageIcon>b4u_logo.png</PackageIcon>
    <ApplicationIcon>b4u_logo (3).ico</ApplicationIcon>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="PDFLayout\template.html" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="b4u_logo (3).ico" />
    <Content Include="PDFLayout\template.html">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Aspose.Words" Version="23.10.0" />
    <PackageReference Include="ceTe.DynamicPDF.HtmlConverter.NET" Version="1.14.0" />
    <PackageReference Include="CTX.WinscpExtensions" Version="5.1.5.1" />
    <PackageReference Include="DevExpress.Document.Processor" Version="22.2.10" />
    <PackageReference Include="ExifLib.PCL" Version="1.0.1" />
    <PackageReference Include="ExifLibNet" Version="2.1.4" />
    <PackageReference Include="FileFormat.Words" Version="23.8.0" />
    <PackageReference Include="IronXL.Excel" Version="2023.10.8" />
    <PackageReference Include="MetadataExtractor" Version="2.9.0-rc2" />
    <PackageReference Include="Microsoft.Extensions.Primitives" Version="9.0.6" />
    <PackageReference Include="Microsoft.Graph" Version="5.36.0" />
    <PackageReference Include="Microsoft.Identity.Client" Version="4.58.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Collections.Specialized" Version="4.3.0" />
    <PackageReference Include="System.Data.OleDb" Version="7.0.0" />
   
	<PackageReference Include="System.ServiceModel.Syndication" Version="8.0.0" />
	  <PackageReference Include="System.ServiceModel.Http" Version="6.0.0" />
	  <PackageReference Include="System.ServiceModel.NetTcp" Version="6.0.0" />
	  <PackageReference Include="System.ServiceModel.Primitives" Version="6.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Properties\Resources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

</Project>