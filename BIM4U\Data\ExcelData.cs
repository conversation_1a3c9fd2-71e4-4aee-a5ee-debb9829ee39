﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIM4U.Data
{
    public class ExcelData
    {
        public int RowIndex { get; set; }
        public string? CompanyName { get; set; }
        public string? CompanyLogoUrl { get; set; }
        public string? ProductName { get; set; }
        public string? ProductCode { get; set; } //This is new column added to the excel sheet to store product code.
        public string? ProductDescription { get; set; }
        public string? ProductSpecification { get; set; }
        public string? ProductImageUrl { get; set; }
        public string? ProductTechnicalImageUrl { get; set; }


        /// <summary>
        /// Contains all additional product image urls.
        /// </summary>
        public List<string> ProductImagesUrl { get; set; } = new List<string>();

        public string? CatalogueUrl { get; set; }

        // Updated colour section properties for Available Colours section
        public string? Colour1Image { get; set; } // Excel column J
        public string? Colour1CategoryTitle { get; set; } // Excel column K
        public string? Colour2Image { get; set; } // Excel column L
        public string? Colour2CategoryTitle { get; set; } // Excel column M
        public string? Colour3Image { get; set; } // Excel column N
        public string? Colour3CategoryTitle { get; set; } // Excel column O

        // Dynamic column headers for image section title
        public string? ProductImageColumnHeader { get; set; } // Header from Excel column G
        public string? TechnicalDrawingColumnHeader { get; set; } // Header from Excel column I
    }
}
