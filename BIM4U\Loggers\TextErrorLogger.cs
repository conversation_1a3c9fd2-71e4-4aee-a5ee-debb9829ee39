﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIM4U.Loggers
{
    /// <summary>
    /// Logs errors to a text file.
    /// </summary>
    public class TextErrorLogger : IDisposable
    {
        string _logFilePath;
        System.IO.StreamWriter _writer;
        private bool disposedValue;

        /// <summary>
        /// Creates a log file at the specified file path and logs errors to it.
        /// </summary>
        /// <param name="logFilePath"></param>
        /// <exception cref="ArgumentNullException"></exception>
        public TextErrorLogger(string logFilePath)
        {
            if (string.IsNullOrEmpty(logFilePath))
            {
                throw new ArgumentNullException(nameof(logFilePath));
            }

            if (!logFilePath.ToLower().EndsWith(".txt"))
            {
                logFilePath += ".txt";
            }
            _logFilePath = logFilePath;
        }

        public void LogError(string message)
        {
            if (_writer == null)
            {
                InitializeFile();
            }
            _writer.WriteLine(message);
        }

        private void InitializeFile()
        {
            if (_writer == null)
            {
                _writer = new System.IO.StreamWriter(_logFilePath, true);
            }
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!disposedValue)
            {
                if (disposing)
                {
                    // TODO: dispose managed state (managed objects)
                }

                _writer?.Flush();
                _writer?.Close();
                _writer?.Dispose();
                _writer = null;

                disposedValue = true;
            }
        }

        // // TODO: override finalizer only if 'Dispose(bool disposing)' has code to free unmanaged resources
        // ~TextErrorLogger()
        // {
        //     // Do not change this code. Put cleanup code in 'Dispose(bool disposing)' method
        //     Dispose(disposing: false);
        // }

        public void Dispose()
        {
            // Do not change this code. Put cleanup code in 'Dispose(bool disposing)' method
            Dispose(disposing: true);
            GC.SuppressFinalize(this);
        }
    }
}
