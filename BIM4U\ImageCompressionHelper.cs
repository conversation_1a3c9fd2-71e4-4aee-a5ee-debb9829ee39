﻿using DevExpress.Emf;
using ExifLib;
using SixLabors.ImageSharp.Metadata.Profiles.Exif;
using System;
using System.Collections.Generic;
using System.Drawing.Imaging;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MetadataExtractor;

namespace BIM4U
{
    public static class ImageCompressionHelper
    {
        public static float CompressionHeightInCM = 7.25f;
        public static float CompressionWidthInCM = 7.25f;

        /// <summary>
        /// Compresses an image from the source path and saves it to the destination path with the default compression width and height. <see cref="ImageCompressionHelper.CompressionWidthInCM"/>
        /// </summary>
        /// <param name="sourcePath"></param>
        /// <param name="destinationPath"></param>
        /// <returns></returns>
        public static bool CompressImage(string sourcePath, string destinationPath)
        {
            CompressAndResizeImage(sourcePath, destinationPath, CompressionWidthInCM, CompressionHeightInCM);
            return true;
        }

        /// <summary>
        /// Compresses and resizes an image from the input path and saves it the output path with the specified width and height in CM, preserving the aspect ratio.
        /// </summary>
        /// <param name="inputPath"></param>
        /// <param name="outputPath"></param>
        /// <param name="targetWidthCm"></param>
        /// <param name="targetHeightCm"></param>
        public static void CompressAndResizeImage(string inputPath, string outputPath, float targetWidthCm, float targetHeightCm)
        {
            //Original Code
            const float inchInCm = 2.54f;
            const float dpi = 300;

            float targetWidthPx = targetWidthCm / inchInCm * dpi;
            float targetHeightPx = targetHeightCm / inchInCm * dpi;

            using (System.Drawing.Image originalImage = System.Drawing.Image.FromFile(inputPath))
            {
                System.Drawing.Image adjustedImage = RotateImageByExifOrientationTag(originalImage);

                float ratioX = targetWidthPx / adjustedImage.Width;
                float ratioY = targetHeightPx / adjustedImage.Height;
                float ratio = Math.Min(ratioX, ratioY);

                int newWidth = (int)(adjustedImage.Width * ratio);
                int newHeight = (int)(adjustedImage.Height * ratio);

                using (Bitmap newImage = new Bitmap(newWidth, newHeight))
                {
                    using (Graphics g = Graphics.FromImage(newImage))
                    {
                        g.DrawImage(adjustedImage, 0, 0, newWidth, newHeight);
                    }

                    newImage.Save(outputPath, ImageFormat.Jpeg);
                }
            }
        }

        public static System.Drawing.Image RotateImageByExifOrientationTag(System.Drawing.Image img)
        {
            int orientationId = 0x112; // Orientation tag ID in EXIF data
            if (img.PropertyIdList.Contains(orientationId))
            {
                var prop = img.GetPropertyItem(orientationId);
                int orientationValue = prop.Value[0];
                switch (orientationValue)
                {
                    case 1:
                        // No rotation required.
                        break;
                    case 2:
                        img.RotateFlip(RotateFlipType.RotateNoneFlipX);
                        break;
                    case 3:
                        img.RotateFlip(RotateFlipType.Rotate180FlipNone);
                        break;
                    case 4:
                        img.RotateFlip(RotateFlipType.Rotate180FlipX);
                        break;
                    case 5:
                        img.RotateFlip(RotateFlipType.Rotate90FlipX);
                        break;
                    case 6:
                        img.RotateFlip(RotateFlipType.Rotate90FlipNone);
                        break;
                    case 7:
                        img.RotateFlip(RotateFlipType.Rotate270FlipX);
                        break;
                    case 8:
                        img.RotateFlip(RotateFlipType.Rotate270FlipNone);
                        break;
                    default:
                        throw new NotImplementedException("An orientation of " + orientationValue + " isn't supported.");
                }
            }

            return img;
        }
    }
}


//System.Drawing.Image originalImage1 = System.Drawing.Image.FromFile(inputPath);

//int orientation = 1; // Default orientation

//try
//{
//    //var directories = ImageMetadataReader.ReadMetadata(inputPath);
//    //var orientationDirectory = directories.FirstOrDefault(d => d.Name == "Exif IFD0" || d.Name == "Exif SubIFD");
//    //if (orientationDirectory != null)
//    //{
//    //    var orientationTag = orientationDirectory.GetTagName;//.GetTag(MetadataExtractor.Tags.Orientation);
//    //    if (orientationTag != null)
//    //    {
//    //        /orientation = orientationTag.GetIntValue() ?? 1;
//    //    }
//    //}

//    //ExifLib.ExifReader.ReadJpeg(originalImage1);


//    //using (ExifReader reader = new ExifReader.ReadJpeg(originalImage1))
//    //{

//    //}
//    //{
//    //    if (reader.GetTagValue<int>(ExifTags.Orientation, out int exifOrientation))
//    //    {
//    //        orientation = exifOrientation;
//    //    }
//    //}



//    // Try to get the orientation metadata
//    foreach (PropertyItem propItem in originalImage1.PropertyItems)
//    {
//        if (propItem.Id == 0x0112) // PropertyTagOrientation
//        {
//            orientation = BitConverter.ToInt16(propItem.Value, 0);
//            break;
//        }


//        // PropertyItem propItem = originalImage1.GetPropertyItem(0x0112); // PropertyTagOrientation
//        if (propItem != null && propItem.Type == 3) // Type 3 means it's a short (16-bit integer)
//        {
//            orientation = BitConverter.ToInt16(propItem.Value, 0);
//        }
//    }
//}
//catch (ArgumentException)
//{
//    // PropertyItem with this ID doesn't exist, use default orientation
//}