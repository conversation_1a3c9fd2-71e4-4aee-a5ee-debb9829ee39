﻿using BIM4U.Data;
using DevExpress.XtraRichEdit.Model;
using Microsoft.Graph.Models;
using Microsoft.Graph.Models.Security;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace BIM4U.Exporters
{
    public class DirectLinkImageDownloader
    {
        List<ExcelData> exceldata;

        #region EventsAndDelegates
        public delegate void DirectLinkImageDownloaderErrorDelegate(string message);
        public event DirectLinkImageDownloaderErrorDelegate? OnError;
        #endregion

        public async void ReadDataAndDownloadImage(Task<List<ExcelData>> excelDatas, string ExportImagePath)
        {
            try
            {
                exceldata = await excelDatas;

                for (int ia = 0; ia < exceldata.Count; ia++)
                {
                    CollectTheImageData(ia, exceldata[ia].ProductName, exceldata[ia].CompanyLogoUrl, exceldata[ia].ProductImageUrl, exceldata[ia].ProductTechnicalImageUrl, exceldata[ia].ProductImagesUrl, exceldata[ia].Colour1Image, exceldata[ia].Colour2Image, exceldata[ia].Colour3Image, ExportImagePath);
                }
            }
            catch (Exception ex)
            {
                OnError?.Invoke(ex.Message);
            }
        }

        private async void CollectTheImageData(int ID, string productname, string companyLogoUrl, string productImageUrl, string productTechnicalImage, List<string> productimagesUrl, string colour1Image, string colour2Image, string colour3Image, string filepath)
        {
            if (!companyLogoUrl.IsNullOrEmpty())
            {
                //To replace the image url
                exceldata[ID].CompanyLogoUrl = DownloadTheImage(productname + "-Company_Logo-", companyLogoUrl, filepath).ToString();
            }
            else
            {
                string msg = string.Format("The Company Logo Url is null");
                throw new ArgumentNullException(msg);
            }

            if (!productImageUrl.IsNullOrEmpty())
            {
                //To replace the image url
                exceldata[ID].ProductImageUrl = DownloadTheImage(productname + "-Product_Image-", productImageUrl, filepath).ToString();
            }
            else
            {
                string msg = string.Format("The product image Url is null");
                throw new ArgumentNullException(msg);
            }

            if (!productTechnicalImage.IsNullOrEmpty())
            {
                //To replace the image url
                exceldata[ID].ProductTechnicalImageUrl = DownloadTheImage(productname + "-Product_Technical_Image-", productTechnicalImage, filepath).ToString();
            }
            else
            {
                string msg = string.Format("The product technical image Url is null");
                throw new ArgumentNullException(msg);
            }

            for (int i = 0; i >= productimagesUrl.Count - 1; i++)
            {
                if (!productimagesUrl[i].IsNullOrEmpty())
                {
                    //To replace the image url
                    exceldata[ID].ProductImagesUrl[i] = DownloadTheImage(productname + "-Product_Images_" + i, productimagesUrl[i], filepath).ToString();
                }
                else
                {
                    string msg = string.Format("The product images " + i + " Url is null");
                    throw new ArgumentNullException(msg);
                }
            }

            // Process colour images for Available Colours section
            if (!colour1Image.IsNullOrEmpty())
            {
                exceldata[ID].Colour1Image = DownloadTheImage(productname + "-Colour1_Image-", colour1Image, filepath).ToString();
            }

            if (!colour2Image.IsNullOrEmpty())
            {
                exceldata[ID].Colour2Image = DownloadTheImage(productname + "-Colour2_Image-", colour2Image, filepath).ToString();
            }

            if (!colour3Image.IsNullOrEmpty())
            {
                exceldata[ID].Colour3Image = DownloadTheImage(productname + "-Colour3_Image-", colour3Image, filepath).ToString();
            }
        }


        private static async Task<string> DownloadTheImage(string productName, string fileUrl, string filepath)
        {
            Uri u = new Uri(fileUrl);

            using (HttpClient client = new HttpClient())
            {
                var response = await client.GetAsync(u);
                if (response.StatusCode == HttpStatusCode.NotFound)
                {
                    string msg = string.Format("The requested resource was not found.");
                    throw new HttpRequestException(msg);
                }
                else if (response.StatusCode == HttpStatusCode.Forbidden)
                {
                    string msg = string.Format("The Request Resource is Fobidden.");
                    throw new HttpRequestException(msg);
                }
                else if (!response.IsSuccessStatusCode)
                {
                    string msg = string.Format($"Error occurred, the status code is: {response.StatusCode}");
                    throw new HttpRequestException(msg);
                }
                else
                {                    
                    using (Stream responseStream = await response.Content.ReadAsStreamAsync(),
                   fileStream2 = new FileStream(filepath + productName, FileMode.Create, FileAccess.Write, FileShare.None))
                    {
                        await responseStream.CopyToAsync(fileStream2);
                        
                    }

                    response.EnsureSuccessStatusCode();
                }
            }

            return filepath + productName;
        }
    }
}
