﻿using BIM4U.Extensions;
using DevExpress.CodeParser;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Office2010.Excel;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Collections.ObjectModel;

namespace BIM4U.Data
{
    public class ImageDownloadManager : IDisposable
    {
        public event OnErrorDelegate OnError;

        public ImageDownloader ImageDownloader { get; private set; }
        public string Folder_CompressedImages { get; private set; }
        public string Folder_UncompressImages { get; private set; }
        public string OneDriveAccessToken { get; set; }

        private Collection<string> UrlsBeingProcessed { get; set; } = new Collection<string>();

        public ImageDownloadManager(string folder_CompressedImages, string folder_UncompressImages)
        {
            ImageDownloader = new ImageDownloader();
            Folder_CompressedImages = folder_CompressedImages;
            Folder_UncompressImages = folder_UncompressImages;
        }

        public ImageDownloadManager(string folder_CompressedImages, string folder_UncompressImages, string oneDriveAccessToken)
            :this(folder_CompressedImages, folder_UncompressImages)
        {
            OneDriveAccessToken = oneDriveAccessToken;
        }


        private object _urlProcessingLock = new object();
        /// <summary>
        /// Checks if the image urls in the object already exists and downloads and compresses them if not. Updates the url to a local path.
        /// </summary>
        /// <param name="excelData"></param>
        /// <exception cref="ArgumentNullException"></exception>
        public async Task<bool> ProcessImageUrls(ExcelData excelData)
        {
            if (excelData == null)
            {
                throw new ArgumentNullException(nameof(excelData));
            }

            try
            {
                excelData.CompanyLogoUrl = await ProcessImageUrl(excelData.CompanyLogoUrl);
                excelData.CatalogueUrl = await ProcessImageUrl(excelData.CatalogueUrl);
                excelData.ProductImageUrl = await ProcessImageUrl(excelData.ProductImageUrl);
                excelData.ProductTechnicalImageUrl = await ProcessImageUrl(excelData.ProductTechnicalImageUrl);

                if (excelData.ProductImagesUrl.Count > 0)
                {
                    for (int i = 0; i <= excelData.ProductImagesUrl.Count - 1; i++)
                    {
                        excelData.ProductImagesUrl[i] = await ProcessImageUrl(excelData.ProductImagesUrl[i]);
                    }
                }

                // Process colour images for Available Colours section
                excelData.Colour1Image = await ProcessImageUrl(excelData.Colour1Image);
                excelData.Colour2Image = await ProcessImageUrl(excelData.Colour2Image);
                excelData.Colour3Image = await ProcessImageUrl(excelData.Colour3Image);

                return true;
            }
            catch (Exception ex)
            {
                OnError?.Invoke(excelData.RowIndex, ex.Message);
                return false;
            }

        }

        /// <summary>
        /// Checks if the image already exists and returns the local path. Alternatively, downloads the image, compresses and returns the path.
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        private async Task<string> ProcessImageUrl(string url)
        {
            //Check that the urls are not null or empty
            //Then get the image name from the url
            //Then check if the file exists in Uncompressed folder
            //If file exists in uncompressed folder - check if exists in compressed folder.
            //If file doesn't exits in compressfolder - compress file and save to compressed folder.
            //If file doesn't exist in uncompressed folder - download and save.

            if (string.IsNullOrEmpty(url))
            {
                return url;
            }

            //The below code is used to allow detect when the same url is currently being downloaded. When multiple threads download the same image, it will result in a file locked exception.
            tryagain:
            bool urlIsBeingProcessed = false;
            lock (_urlProcessingLock)
            {
                if (UrlsBeingProcessed.Contains(url))
                {
                    urlIsBeingProcessed = true;
                }
                else
                {
                    UrlsBeingProcessed.Add(url);
                }
            }

            //If url is being processed, sleep thread for a second and then check again.
            if (urlIsBeingProcessed)
            {
                Thread.Sleep(1000);
                goto tryagain;
            }

            try
            {
                //Check if file exists in Compressed folder
                if (File.Exists(GetFilePath(Folder_CompressedImages, url, ".jpg")))
                {
                    return GetFilePath(Folder_CompressedImages, url, ".jpg");
                }

                //Check if file exists in uncompressed folder
                if (File.Exists(GetFilePath(Folder_UncompressImages, url, null)))
                {
                    //File exists in uncompressed but hasn't bee compressed yet. Compress and return path.
                    string compressedPath = GetFilePath(Folder_CompressedImages, url, ".jpg");
                    ImageCompressionHelper.CompressImage(GetFilePath(Folder_UncompressImages, url, null), compressedPath);
                    return compressedPath;
                }
                else
                {
                    //File doesn't exist in either folder. Download, compress and return path.
                    //Download image.
                    byte[] data = await ImageDownloader.DownloadImageAsync(url, OneDriveAccessToken);
                    //Save image.
                    SaveImageToUncompressedFolder(url, data);
                    //Compress image.
                    string compressedPath = GetFilePath(Folder_CompressedImages, url, ".jpg");
                    ImageCompressionHelper.CompressImage(GetFilePath(Folder_UncompressImages, url, null), compressedPath);
                    return compressedPath;
                }
            }
            catch (Exception)
            {

                throw;
            }
            finally
            {
                lock (_urlProcessingLock)
                {
                    if (UrlsBeingProcessed.Contains(url))
                    {
                        UrlsBeingProcessed.Remove(url);
                    }
                }
            }
        }

        public string GetFilePath(string folder, string imageUrl, string extension)
        {
            string fileName = GetImageNameFromUrl(imageUrl, extension);
            fileName = fileName.AsFileSafeString();
            var filePath = System.IO.Path.Combine(folder, fileName);
            return filePath;
        }

        public void SaveImageToCompressedFolder(string imageUrl, byte[] imageBytes)
        {
            var filePath = GetFilePath(Folder_CompressedImages, imageUrl, ".jpg");
            File.WriteAllBytes(filePath, imageBytes);
        }

        public void SaveImageToUncompressedFolder(string imageUrl, byte[] imageBytes)
        {
            var filePath = GetFilePath(Folder_UncompressImages, imageUrl, null);
            File.WriteAllBytes(filePath, imageBytes);
        }

        public bool DoesUncompressedImageExist(string imageUrl)
        {
            var filePath = GetFilePath(Folder_UncompressImages, imageUrl, null);
            return File.Exists(filePath);
        }

        public bool DoesCompressedImageExist(string imageUrl)
        {
            var filePath = GetFilePath(Folder_CompressedImages, imageUrl, ".jpg");
            return File.Exists(filePath);
        }

        public string GetImageNameFromUrl(string url, string extension)
        {
            //Process url for weird links.
            if (url.Contains("&parent"))
            {
                var sub = url.Split("&parent", StringSplitOptions.RemoveEmptyEntries).ElementAtOrDefault(0);
                if (!string.IsNullOrEmpty(sub))
                {
                    url = sub;
                }
            }

            Uri uri = new Uri(url);
            string queryOrPath;
            if (!string.IsNullOrEmpty(uri.Query))
            {
                queryOrPath = uri.Query;
            }
            else
            {
                queryOrPath = uri.PathAndQuery;
            }

            queryOrPath = DecodeUrl(queryOrPath);

            string originalExtension = System.IO.Path.GetExtension(queryOrPath);

            if (!string.IsNullOrEmpty(originalExtension))
            {
                if (originalExtension.ToLower() != ".jpg" && originalExtension.ToLower() != ".png" && originalExtension.ToLower() != ".jpeg")
                {
                    throw new InvalidDataException("Invalid image format - " + originalExtension);
                }
            }

            if (string.IsNullOrEmpty(extension))
            {
                extension = originalExtension;
            }

            string imageName = System.IO.Path.GetFileNameWithoutExtension(queryOrPath) + extension;            
           
            return imageName;
        }

        public string DecodeUrl(string url)
        {
            return HttpUtility.UrlDecode(url);
        }

        #region Dispose
        private bool disposedValue;

        protected virtual void Dispose(bool disposing)
        {
            if (!disposedValue)
            {
                if (disposing)
                {
                    // TODO: dispose managed state (managed objects)
                }

                ImageDownloader?.Dispose();
                ImageDownloader = null;
                disposedValue = true;
            }
        }

        // // TODO: override finalizer only if 'Dispose(bool disposing)' has code to free unmanaged resources
        // ~ImageDownloadManager()
        // {
        //     // Do not change this code. Put cleanup code in 'Dispose(bool disposing)' method
        //     Dispose(disposing: false);
        // }

        public void Dispose()
        {
            // Do not change this code. Put cleanup code in 'Dispose(bool disposing)' method
            Dispose(disposing: true);
            GC.SuppressFinalize(this);
        }
        #endregion
    }
}
