﻿using Azure.Core;
using DocumentFormat.OpenXml.Bibliography;
using Newtonsoft.Json;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using System.Collections.ObjectModel;

namespace BIM4U.Data
{
    public class ImageDownloader : IDisposable

    {
        public ConcurrentBag <HttpClient> HttpClientCollection { get; private set; }

        public ImageDownloader()
        {
            HttpClientCollection = new ConcurrentBag<HttpClient>();
        }

        /// <summary>
        /// Downloads the image from the specified url. If the token is present, the OneDrive download logic will take place.
        /// </summary>
        /// <param name="imageUrl"></param>
        /// <param name="token"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public async Task<byte[]> DownloadImageAsync(string imageUrl, string token)
        {
            if (string.IsNullOrEmpty(imageUrl))
            {
                throw new ArgumentNullException(nameof(imageUrl));
            }

            if (string.IsNullOrEmpty(token))
            {
                return await DownloadImageFromUrlAsync(imageUrl);
            }
            else
            {
                return await DownloadImageFromUrlOneDriveAsync(imageUrl, token);
            }
        }

        public async Task<byte[]> DownloadImageFromUrlAsync(string imageUrl)
        {
            if (string.IsNullOrEmpty(imageUrl))
            {
                throw new ArgumentNullException(nameof(imageUrl));
            }

            var httpClient = GetHttpClient();

            if (httpClient.DefaultRequestHeaders.Authorization != null)
            {
                httpClient.DefaultRequestHeaders.Authorization = null;
            }
            try
            {
                Uri u = new Uri(imageUrl);

                var response = await httpClient.GetAsync(u);
                if (response.StatusCode == HttpStatusCode.NotFound)
                {
                    string msg = string.Format("The requested resource was not found.");
                    throw new HttpRequestException(msg);
                }
                else if (response.StatusCode == HttpStatusCode.Forbidden)
                {
                    string msg = string.Format("The Request Resource is Fobidden.");
                    throw new HttpRequestException(msg);
                }
                else if (!response.IsSuccessStatusCode)
                {
                    string msg = string.Format($"Error occurred, the status code is: {response.StatusCode}");
                    throw new HttpRequestException(msg);
                }
                else
                {
                    response.EnsureSuccessStatusCode();
                    byte[] imageBytes = await response.Content.ReadAsByteArrayAsync();
                    // using (Stream responseStream = await response.Content.ReadAsByteArrayAsync(),
                    //fileStream2 = new FileStream(filepath + productName, FileMode.Create, FileAccess.Write, FileShare.None))
                    // {
                    //     await responseStream.CopyToAsync(fileStream2);
                    // }

                    return imageBytes;
                }
            }
            catch (Exception)
            {

                throw;
            }
            finally
            {
                StoreHttpClient(httpClient);
            }
        }

        public async Task<byte[]> DownloadImageFromUrlOneDriveAsync(string imageUrl, string token)
        {

            if (string.IsNullOrEmpty(imageUrl))
            {
                throw new ArgumentNullException(nameof(imageUrl));
            }

            var httpClient = GetHttpClient();

            if (httpClient.DefaultRequestHeaders.Authorization == null)
            {
                //Set authentication header
                httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            }

            try
            {
                string sharingUrl = imageUrl;

                string encodedUrl = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(sharingUrl)).TrimEnd('=').Replace('/', '_').Replace('+', '-');
                string requestUrl = $"https://graph.microsoft.com/v1.0/shares/u!{encodedUrl}/driveItem";

                var response1 = await httpClient.GetAsync(requestUrl);

                if (response1.IsSuccessStatusCode)
                {
                    string responseBody = await response1.Content.ReadAsStringAsync();
                    dynamic? driveItem = JsonConvert.DeserializeObject(responseBody);

                    string driveItemId = driveItem.id;
                    Console.WriteLine("Drive Item ID: " + driveItemId);

                    string fileEndpoint = $"https://graph.microsoft.com/v1.0/me/drive/items/{driveItemId}/content";

                    var fileResponse = await httpClient.GetAsync(fileEndpoint);

                    if (fileResponse.IsSuccessStatusCode)
                    {
                        byte[] imageBytes = await fileResponse.Content.ReadAsByteArrayAsync();
                        return imageBytes;
                        // var fileContent = await fileResponse.Content.ReadAsStreamAsync();
                        // Save fileContent to a file or process it as needed
                        //using (Stream responseStream = await fileResponse.Content.ReadAsStreamAsync(),
                        //  fileStream2 = new FileStream(filepath + productName, FileMode.Create, FileAccess.Write, FileShare.None))
                        //{
                        //    await responseStream.CopyToAsync(fileStream2);
                        //}
                    }
                    else
                    {
                        string msg = string.Format($"There was an error downloading the image {fileResponse.StatusCode.ToString()}");
                        throw new HttpRequestException(msg);
                    }

                }
                else
                {
                    string msg = string.Format($"There was an error downloading the image {response1.StatusCode.ToString()}");
                    throw new HttpRequestException(msg);
                }

                //var response = await httpClient.GetAsync(imageUrl);

                //if (response.IsSuccessStatusCode)
                //{
                //    string sharingUrl = imageUrl;

                //    string encodedUrl = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(sharingUrl)).TrimEnd('=').Replace('/', '_').Replace('+', '-');
                //    string requestUrl = $"https://graph.microsoft.com/v1.0/shares/u!{encodedUrl}/driveItem";

                //    var response1 = await httpClient.GetAsync(requestUrl);

                //    if (response1.IsSuccessStatusCode)
                //    {
                //        string responseBody = await response1.Content.ReadAsStringAsync();
                //        dynamic? driveItem = JsonConvert.DeserializeObject(responseBody);

                //        string driveItemId = driveItem.id;
                //        Console.WriteLine("Drive Item ID: " + driveItemId);

                //        string fileEndpoint = $"https://graph.microsoft.com/v1.0/me/drive/items/{driveItemId}/content";

                //        var fileResponse = await httpClient.GetAsync(fileEndpoint);

                //        if (fileResponse.IsSuccessStatusCode)
                //        {
                //            byte[] imageBytes = await fileResponse.Content.ReadAsByteArrayAsync();
                //            return imageBytes;
                //            // var fileContent = await fileResponse.Content.ReadAsStreamAsync();
                //            // Save fileContent to a file or process it as needed
                //            //using (Stream responseStream = await fileResponse.Content.ReadAsStreamAsync(),
                //            //  fileStream2 = new FileStream(filepath + productName, FileMode.Create, FileAccess.Write, FileShare.None))
                //            //{
                //            //    await responseStream.CopyToAsync(fileStream2);
                //            //}
                //        }
                //        else
                //        {
                //            string msg = string.Format($"There was an error downloading the image {fileResponse.StatusCode.ToString()}");
                //            throw new HttpRequestException(msg);
                //        }

                //    }
                //    else
                //    {
                //        string msg = string.Format($"There was an error downloading the image {response1.StatusCode.ToString()}");
                //        throw new HttpRequestException(msg);
                //    }
                //}
                //else
                //{

                //    string msg = string.Format($"There was an error downloading the image {response.StatusCode.ToString()}");
                //    throw new HttpRequestException(msg);
                //}

            }
            catch (Exception)
            {

                throw;
            }
            finally
            {
                StoreHttpClient(httpClient);
            }
        }


        private object _httpClientLock = new object();

        /// <summary>
        /// Attempts to return an existing client from the collection and removes it from the collection if it exists.
        /// </summary>
        /// <returns></returns>
        private HttpClient GetHttpClient()
        {
            lock (_httpClientLock)
            {
                HttpClient availableClient;
                if (HttpClientCollection.TryTake(out availableClient))
                {
                    return availableClient;
                }
                else
                {
                    return new HttpClient();
                }
            }
        }

        /// <summary>
        /// Stores the instance of the httpclient back into the collection for another thread to use.
        /// </summary>
        /// <param name="client"></param>
        private void StoreHttpClient(HttpClient client)
        {
            HttpClientCollection.Add(client);
        }

        /// <summary>
        /// This method sends a HEAD request to the URL and returns true if the response status code indicates success (i.e., it's in the range 200-299). 
        /// If the image doesn't exist, the server will likely respond with a 404 status code, and IsSuccessStatusCode will be false.
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        public async Task<bool> ImageExists(string url)
        {
            using (HttpClient client = new HttpClient())
            {
                var request = new HttpRequestMessage(HttpMethod.Head, url);
                var response = await client.SendAsync(request);

                return response.IsSuccessStatusCode;
            }
        }


        #region Dispose
        private bool disposedValue;

        protected virtual void Dispose(bool disposing)
        {
            if (!disposedValue)
            {
                if (disposing)
                {
                    // TODO: dispose managed state (managed objects)
                }

                if (HttpClientCollection != null)
                {
                    // Convert to array to avoid modification during enumeration
                    var clients = HttpClientCollection.ToArray();
                    foreach (var item in clients)
                    {
                        item?.Dispose();
                    }
                    HttpClientCollection = null;
                }

                disposedValue = true;
            }
        }

        // // TODO: override finalizer only if 'Dispose(bool disposing)' has code to free unmanaged resources
        // ~ImageDownloader()
        // {
        //     // Do not change this code. Put cleanup code in 'Dispose(bool disposing)' method
        //     Dispose(disposing: false);
        // }

        public void Dispose()
        {
            // Do not change this code. Put cleanup code in 'Dispose(bool disposing)' method
            Dispose(disposing: true);
            GC.SuppressFinalize(this);
        }
        #endregion
    }
}