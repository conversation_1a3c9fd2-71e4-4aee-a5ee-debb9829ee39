﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIM4U.Extensions
{
    public static class StringExtensions
    {
        public static string AsFileSafeString(this string value, string replaceInvalidWith = "_")
        {
            if (string.IsNullOrEmpty(value))
            {
                return value;
            }

            foreach (var item in Path.GetInvalidFileNameChars())
            {
                value = value.Replace(item.ToString(), replaceInvalidWith);
            }

            return value;
        }
    }
}
