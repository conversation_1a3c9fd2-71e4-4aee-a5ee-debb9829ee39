using IronXL;
using System.Data;
using System.Data.OleDb;
using System.Windows.Forms;
using System.Reflection;
using ceTe.DynamicPDF.HtmlConverter;
using System;
using System.Security.Policy;
using static System.Net.WebRequestMethods;
using Microsoft.Graph;
using Microsoft.Graph.Authentication;
using Microsoft.Identity.Client;
using DocumentFormat.OpenXml.Bibliography;
using System.Net.Http.Headers;
using Azure.Core;
using Microsoft.Graph.Models;
using Microsoft.Graph.Models.Security;
using Microsoft.Graph.Drives.Item.Items.Item.GetActivitiesByIntervalWithStartDateTimeWithEndDateTimeWithInterval;
using DocumentFormat.OpenXml.Office2010.Excel;
using DocumentFormat.OpenXml.Wordprocessing;
using Newtonsoft.Json;
using System.IO;
using System.Net.Http;

namespace BIM4U
{
    public partial class Form1 : Form
    {
        List<string> CompanyName = new List<string>();
        List<string> Brand = new List<string>();
        List<string> Product = new List<string>();
        List<string> ProductCode = new List<string>(); //New list for Product Code
        List<string> ProductDescription = new List<string>();
        List<string> ProductImage = new List<string>();
        List<string> ProductLineDrawing = new List<string>();
        List<string> ProductDetail1 = new List<string>();
        List<string> ProductDetail2 = new List<string>();

        List<string> FileName = new List<string>();
        List<string> ProductDescription1 = new List<string>();
        List<string> ProductImage1 = new List<string>();
        List<string> ProductLineDrawing1 = new List<string>();
        List<string> ProductDetailSpecification = new List<string>();

        //New Setup
        List<string> Companyv1 = new List<string>();
        List<string> CompanyLogov1 = new List<string>();
        List<string> productv1 = new List<string>();
        List<string> ProductCodev1 = new List<string>();
        List<string> ProductDescriptionV1 = new List<string>();
        List<string> productSpecificationv1 = new List<string>();
        List<string> ProductimageUrl = new List<string>();
        List<string> ProductimageUrl1 = new List<string>();
        List<string> ProductimageUrl2 = new List<string>();
        List<string> ProductimageUrl3 = new List<string>();
        List<string> productTechnicalUrl = new List<string>();
        List<string> Catalogue = new List<string>();

        public string ErrorCode = "";

        //Save word format
        string GlobalFilePath = "";

        string GlobalImagePath = "";

        public Form1()
        {
            InitializeComponent();
            erroe.Visible = false;
        }

        private void button1_Click(object sender, EventArgs e)
        {
            try
            {
                erroe.Visible = false;
                ReadNewFormat();

                ExportToPDF();

            }
            catch (Exception ex)
            {
                MessageBox.Show("Error Message " + ex.Message);
            }
        }


        public void GetSeparateDocs()
        {
            OpenFileDialog Openfile = new OpenFileDialog();

            string filePath = @"C:\\Users\\<USER>\\Downloads\\Insight blueprint\\Financial Sample.xlsx";
            string ExcelData = "";
            int DrCount = 0;
            int fileCount = 0;

            //Open the file
            if (Openfile.ShowDialog() == DialogResult.OK)
            {
                filePath = Openfile.FileName;
                GlobalFilePath = filePath;
                lbFileName.Text = filePath;

                MessageBox.Show("The File You Selected Is " + filePath);

                MessageBox.Show("Please Select Where You Would Like To Export Your Files");

                string con = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + filePath + ";Extended Properties=\"Excel 12.0;HDR=YES;IMEX=1\";";

                using (OleDbConnection connection = new OleDbConnection(con))
                {
                    connection.Open();
                    OleDbCommand command = new OleDbCommand("select * from [Sheet1$]", connection);
                    using (OleDbDataReader dr = command.ExecuteReader())
                    {
                        fileCount = dr.FieldCount;

                        while (dr.Read())
                        {
                            DrCount++;

                            //Secound Column
                            if (dr[0].ToString() != "")
                            {
                                FileName.Add(dr?[0].ToString());
                                ProductDescription1.Add(dr?[3].ToString());
                                ProductImage1.Add(dr?[6].ToString());
                                ProductLineDrawing1.Add(dr?[8].ToString());
                                ProductDetailSpecification.Add(dr?[4].ToString());
                                //ProductCode.Add(dr?[5].ToString());
                                ProductimageUrl.Add(dr?[6].ToString());
                                productTechnicalUrl.Add(dr?[8].ToString());
                                ProductimageUrl1.Add(dr?[9].ToString());
                                ProductimageUrl2.Add(dr?[10].ToString());
                                ProductimageUrl3.Add(dr?[11].ToString());
                                ProductimageUrl3.Add(dr?[12].ToString());
                                Catalogue.Add(dr?[13].ToString());
                            }
                        }
                    }

                    connection.Close();
                }
            }
            else if (Openfile.ShowDialog() == DialogResult.Cancel)
            {
                MessageBox.Show("The operation has been Cancelled");
            }
        }


        public void GetAllData()
        {
            OpenFileDialog Openfile = new OpenFileDialog();

            string filePath = @"C:\\Users\\<USER>\\Downloads\\Insight blueprint\\Financial Sample.xlsx";
            string ExcelData = "";
            int DrCount = 0;
            int fileCount = 0;

            //Open the file
            if (Openfile.ShowDialog() == DialogResult.OK)
            {
                filePath = Openfile.FileName;
                GlobalFilePath = filePath;
                lbFileName.Text = filePath;

                MessageBox.Show("The File You Selected Is " + filePath);

                MessageBox.Show("Please Select Where You Would Like To Export Your Files");

                string con = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + filePath + ";Extended Properties=\"Excel 12.0;HDR=YES;IMEX=1\";";

                using (OleDbConnection connection = new OleDbConnection(con))
                {
                    connection.Open();
                    OleDbCommand command = new OleDbCommand("select * from [Sheet1$]", connection);
                    using (OleDbDataReader dr = command.ExecuteReader())
                    {
                        fileCount = dr.FieldCount;

                        while (dr.Read())
                        {
                            DrCount++;

                            //ExcelData += dr?[0] + Environment.NewLine;
                            //Gets data from all columns using index number

                            //first Column 
                            //dr[0].ToString();

                            //Secound Column
                            if (dr[0].ToString() != "")
                            {
                                Companyv1.Add(dr?[0].ToString());
                                CompanyLogov1.Add(dr?[1].ToString());
                                productv1.Add(dr?[2].ToString());
                               // ProductCode.Add(dr?[5].ToString());
                                ProductDescriptionV1.Add(dr?[3].ToString());
                                productSpecificationv1.Add(dr?[4].ToString());
                                //image
                                ProductimageUrl.Add(dr?[6].ToString());
                                //Technical image
                                productTechnicalUrl.Add(dr?[8].ToString());
                                ProductimageUrl1.Add(dr?[9].ToString());
                                ProductimageUrl2.Add(dr?[10].ToString());
                                ProductimageUrl3.Add(dr?[11].ToString());
                                ProductimageUrl3.Add(dr?[12].ToString());

                                Catalogue.Add(dr?[13].ToString());

                                //CompanyName.Add(dr?[0].ToString());
                                //Brand.Add(dr?[1].ToString());
                                //Product.Add(dr?[2].ToString());
                                //ProductDescription.Add(dr?[3].ToString());
                                //ProductImage.Add(dr?[5].ToString());
                                //ProductLineDrawing.Add(dr?[7].ToString());
                                //ProductDetail1.Add(dr?[8].ToString());

                                FileName.Add(dr?[0].ToString());
                                ProductDescription1.Add(dr?[3].ToString());
                                ProductImage1.Add(dr?[6].ToString());
                                ProductLineDrawing1.Add(dr?[8].ToString());
                                ProductDetailSpecification.Add(dr?[4].ToString());
                            }
                        }
                    }

                    connection.Close();
                }
            }
            else if (Openfile.ShowDialog() == DialogResult.Cancel)
            {
                MessageBox.Show("The operation has been Cancelled");
            }
        }

        public async void ExportToPDF()
        {
            string html1 = "";
            string ImageHtml = "";
            string CompanyLogoghtml = "";

            string test = "";

            string path = "";
            string Imagepath = @"C:\Users\<USER>\Downloads\bim4you.jpg";

            //Check how many images it has. if the image path is null, Skip

            ConversionOptions options = new ConversionOptions(ceTe.DynamicPDF.HtmlConverter.PageSize.A3, PageOrientation.Landscape, 50.0f);

            SaveFileDialog saveFile = new SaveFileDialog();

            // Replace with your application (client) ID
            string clientId = "22a9ad7f-fbca-41cd-9a6f-b766d94318ff";     //"22a9ad7f-fbca-41cd-9a6f-b766d94318ff";  //"5cb5886e-e74f-4279-816b-d06604e5b60f";      //"2622225b-709f-4917-a275-36fa0d67ce17";

            // // Replace with your tenant ID
            string tenantId = "7b57e4fd-85fc-4a62-8f79-bfc9313a53a2";    //"7b57e4fd-85fc-4a62-8f79-bfc9313a53a2";    //"193daea9-73e0-467d-a9c8-2cf78c9485ba";      //"819f729d-023e-421e-b387-09d3afd1b668";

            // // Replace with the redirect URI specified in your Azure AD app registration
            string redirectUri = "https://login.microsoftonline.com/common/oauth2/nativeclient"; //https://login.microsoftonline.com/common/oauth2/nativeclient";

            // // Replace with the requested Microsoft Graph API scopes
            string[] scopes = { "files.read", "files.read.all", "files.readwrite", "files.readwrite.all" };

            string authority = "https://login.microsoftonline.com/7b57e4fd-85fc-4a62-8f79-bfc9313a53a2";      /*7b57e4fd-85fc-4a62-8f79-bfc9313a53a2"; //*/       //193daea9-73e0-467d-a9c8-2cf78c9485ba

            var app = PublicClientApplicationBuilder
           .Create(clientId)
           .WithRedirectUri(redirectUri)
           .WithAuthority(new Uri(authority))
           .Build();

            var accounts = await app.GetAccountsAsync();

            AuthenticationResult authResult = null;

            try
            {
                authResult = await app.AcquireTokenInteractive(scopes)
                    .WithAccount(accounts.FirstOrDefault()) // You can choose the user account if necessary
                    .WithPrompt(Microsoft.Identity.Client.Prompt.SelectAccount) // Prompt for account selection
                    .ExecuteAsync();
            }
            catch (MsalException ex)
            {
                MessageBox.Show(ex.Message);
            }

            if (authResult != null)
            {

                if (saveFile.ShowDialog() == DialogResult.OK)
                {
                    path = saveFile.FileName;

                    for (int i = 0; i <= CompanyLogov1.Count - 1; i++)
                    {
                        NoOfPdfs.Text = i + " Of " + (CompanyLogov1.Count - 1) + " Pdfs";

                        #region AddAllImages

                        if (CompanyLogov1[i] != null)
                        {
                            string sharingUrl = CompanyLogov1[i];
                            string encodedUrl = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(sharingUrl)).TrimEnd('=').Replace('/', '_').Replace('+', '-');
                            string sharesRequestUrl = $"https://graph.microsoft.com/v1.0/shares/u!{encodedUrl}/driveItem";

                            var httpClient = new HttpClient();
                            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", authResult.AccessToken);
                            var sharesResponse = await httpClient.GetAsync(sharesRequestUrl);

                            if (!sharesResponse.IsSuccessStatusCode)
                            {
                                // Handle error
                                return;
                            }

                            string sharesResponseBody = await sharesResponse.Content.ReadAsStringAsync();
                            dynamic sharesResponseData = JsonConvert.DeserializeObject(sharesResponseBody);
                            string driveItemId = sharesResponseData.id;

                            // Step 3: Get Direct Download Link
                            string directLinkRequestUrl = $"https://graph.microsoft.com/v1.0/me/drive/items/{driveItemId}/content";
                            var directLinkResponse = await httpClient.GetAsync(directLinkRequestUrl);

                            if (directLinkResponse.IsSuccessStatusCode)
                            {
                                string directLink = directLinkResponse.RequestMessage.RequestUri.ToString();
                                Console.WriteLine("Direct Link: " + directLink);

                                test = directLink;

                                CompanyLogoghtml = "<img src=\"" + test /*ProductimageUrl[i]*/ + "\" width=\"200\" height=\"200\" />\r\n";

                                //You can now use this direct link in your HTML
                            }
                            else
                            {
                                // Handle error
                            }
                        }

                        if (ProductimageUrl[i] != "")
                        {

                            string sharingUrl = ProductimageUrl[i];
                            string encodedUrl = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(sharingUrl)).TrimEnd('=').Replace('/', '_').Replace('+', '-');
                            string sharesRequestUrl = $"https://graph.microsoft.com/v1.0/shares/u!{encodedUrl}/driveItem";

                            var httpClient = new HttpClient();
                            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", authResult.AccessToken);
                            var sharesResponse = await httpClient.GetAsync(sharesRequestUrl);

                            if (!sharesResponse.IsSuccessStatusCode)
                            {
                                // Handle error
                                return;
                            }

                            string sharesResponseBody = await sharesResponse.Content.ReadAsStringAsync();
                            dynamic sharesResponseData = JsonConvert.DeserializeObject(sharesResponseBody);
                            string driveItemId = sharesResponseData.id;

                            // Step 3: Get Direct Download Link
                            string directLinkRequestUrl = $"https://graph.microsoft.com/v1.0/me/drive/items/{driveItemId}/content";
                            var directLinkResponse = await httpClient.GetAsync(directLinkRequestUrl);

                            if (directLinkResponse.IsSuccessStatusCode)
                            {
                                string directLink = directLinkResponse.RequestMessage.RequestUri.ToString();
                                Console.WriteLine("Direct Link: " + directLink);

                                test = directLink;

                                ImageHtml += "<img src=\"" + test /*ProductimageUrl[i]*/ + "\" width=\"300\" height=\"300\" />\r\n";

                                //You can now use this direct link in your HTML
                            }
                            else
                            {
                                // Handle error
                            }
                        }

                        if (productTechnicalUrl[i] != "")
                        {

                            string sharingUrl = productTechnicalUrl[i];
                            string encodedUrl = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(sharingUrl)).TrimEnd('=').Replace('/', '_').Replace('+', '-');
                            string sharesRequestUrl = $"https://graph.microsoft.com/v1.0/shares/u!{encodedUrl}/driveItem";

                            var httpClient = new HttpClient();
                            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", authResult.AccessToken);
                            var sharesResponse = await httpClient.GetAsync(sharesRequestUrl);

                            if (!sharesResponse.IsSuccessStatusCode)
                            {
                                // Handle error
                                return;
                            }

                            string sharesResponseBody = await sharesResponse.Content.ReadAsStringAsync();
                            dynamic sharesResponseData = JsonConvert.DeserializeObject(sharesResponseBody);
                            string driveItemId = sharesResponseData.id;

                            // Step 3: Get Direct Download Link
                            string directLinkRequestUrl = $"https://graph.microsoft.com/v1.0/me/drive/items/{driveItemId}/content";
                            var directLinkResponse = await httpClient.GetAsync(directLinkRequestUrl);

                            if (directLinkResponse.IsSuccessStatusCode)
                            {
                                string directLink = directLinkResponse.RequestMessage.RequestUri.ToString();
                                Console.WriteLine("Direct Link: " + directLink);

                                test = directLink;

                                ImageHtml += "<img src=\"" + test /*productTechnicalUrl[i]*/ + "\" width=\"300\" height=\"300\" />\r\n";

                                // You can now use this direct link in your HTML
                            }
                            else
                            {
                                // Handle error
                            }

                        }

                        if (ProductimageUrl1[i] != "")
                        {
                            string sharingUrl = ProductimageUrl1[i];
                            string encodedUrl = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(sharingUrl)).TrimEnd('=').Replace('/', '_').Replace('+', '-');
                            string sharesRequestUrl = $"https://graph.microsoft.com/v1.0/shares/u!{encodedUrl}/driveItem";

                            var httpClient = new HttpClient();
                            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", authResult.AccessToken);
                            var sharesResponse = await httpClient.GetAsync(sharesRequestUrl);

                            if (!sharesResponse.IsSuccessStatusCode)
                            {
                                // Handle error
                                return;
                            }

                            string sharesResponseBody = await sharesResponse.Content.ReadAsStringAsync();
                            dynamic sharesResponseData = JsonConvert.DeserializeObject(sharesResponseBody);
                            string driveItemId = sharesResponseData.id;

                            // Step 3: Get Direct Download Link
                            string directLinkRequestUrl = $"https://graph.microsoft.com/v1.0/me/drive/items/{driveItemId}/content";
                            var directLinkResponse = await httpClient.GetAsync(directLinkRequestUrl);

                            if (directLinkResponse.IsSuccessStatusCode)
                            {
                                string directLink = directLinkResponse.RequestMessage.RequestUri.ToString();
                                Console.WriteLine("Direct Link: " + directLink);

                                test = directLink;

                                ImageHtml += "<img src=\"" + test /*ProductimageUrl1[i]*/ + "\" width=\"300\" height=\"300\" />\r\n";

                                // You can now use this direct link in your HTML
                            }
                            else
                            {
                                // Handle error
                            }

                        }

                        if (ProductimageUrl2[i] != "")
                        {
                            string sharingUrl = ProductimageUrl2[i];
                            string encodedUrl = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(sharingUrl)).TrimEnd('=').Replace('/', '_').Replace('+', '-');
                            string sharesRequestUrl = $"https://graph.microsoft.com/v1.0/shares/u!{encodedUrl}/driveItem";

                            var httpClient = new HttpClient();
                            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", authResult.AccessToken);
                            var sharesResponse = await httpClient.GetAsync(sharesRequestUrl);

                            if (!sharesResponse.IsSuccessStatusCode)
                            {
                                // Handle error
                                return;
                            }

                            string sharesResponseBody = await sharesResponse.Content.ReadAsStringAsync();
                            dynamic sharesResponseData = JsonConvert.DeserializeObject(sharesResponseBody);
                            string driveItemId = sharesResponseData.id;

                            // Step 3: Get Direct Download Link
                            string directLinkRequestUrl = $"https://graph.microsoft.com/v1.0/me/drive/items/{driveItemId}/content";
                            var directLinkResponse = await httpClient.GetAsync(directLinkRequestUrl);

                            if (directLinkResponse.IsSuccessStatusCode)
                            {
                                string directLink = directLinkResponse.RequestMessage.RequestUri.ToString();
                                Console.WriteLine("Direct Link: " + directLink);

                                test = directLink;

                                ImageHtml += "<img src=\"" + test /*ProductimageUrl2[i]*/ + "\" width=\"300\" height=\"300\" />\r\n";

                                //You can now use this direct link in your HTML
                            }
                            else
                            {
                                // Handle error
                            }
                        }

                        if (ProductimageUrl3[i] != "")
                        {
                            string sharingUrl = ProductimageUrl3[i];
                            string encodedUrl = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(sharingUrl)).TrimEnd('=').Replace('/', '_').Replace('+', '-');
                            string sharesRequestUrl = $"https://graph.microsoft.com/v1.0/shares/u!{encodedUrl}/driveItem";

                            var httpClient = new HttpClient();
                            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", authResult.AccessToken);
                            var sharesResponse = await httpClient.GetAsync(sharesRequestUrl);

                            if (!sharesResponse.IsSuccessStatusCode)
                            {
                                // Handle error
                                return;
                            }

                            string sharesResponseBody = await sharesResponse.Content.ReadAsStringAsync();
                            dynamic sharesResponseData = JsonConvert.DeserializeObject(sharesResponseBody);
                            string driveItemId = sharesResponseData.id;

                            // Step 3: Get Direct Download Link
                            string directLinkRequestUrl = $"https://graph.microsoft.com/v1.0/me/drive/items/{driveItemId}/content";
                            var directLinkResponse = await httpClient.GetAsync(directLinkRequestUrl);

                            if (directLinkResponse.IsSuccessStatusCode)
                            {
                                string directLink = directLinkResponse.RequestMessage.RequestUri.ToString();
                                Console.WriteLine("Direct Link: " + directLink);

                                test = directLink;

                                ImageHtml += "<img src=\"" + test /*ProductimageUrl3[i]*/ + "\" width=\"300\" height=\"300\" />\r\n";

                                //You can now use this direct link in your HTML
                            }
                            else
                            {
                                // Handle error
                            }
                        }
                        #endregion

                        html1 = "<!DOCTYPE html>\r\n" +
                             "<html lang=\"en\" xmlns=\"http://www.w3.org/1999/xhtml\">\r\n" +
                             "<head>\r\n" +
                             "<!-- Required meta tags -->\r\n" +
                             "<meta charset=\"utf-8\" />\r\n" +
                             "<meta name=\"viewport\"\r\n" +
                             "content=\"width=device-width, initial-scale=1\" />\r\n" +
                             "<!-- Bootstrap CSS -->\r\n" +
                             "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.1.1/dist/css/bootstrap.min.css\" +\r\n\"" +
                             "rel=\" stylesheet\"\r\n" +
                             "\"integrity=\" sha384-F3w7mX95PdgyTmZZMECAngseQB83DfGTowi0iMjiWaeVhAn4FJkqJByhZMI3AhiU\"\r\n" +
                             "\"crossorigin=\" anonymous\" />\r\n" +
                             "<style>\r\n" +
                             "/* Print styles for consistent background across all pages */\r\n" +
                             "@media print {\r\n" +
                             "  @page {\r\n" +
                             "    background: #bfbfbf;\r\n" +
                             "    -webkit-print-color-adjust: exact;\r\n" +
                             "    print-color-adjust: exact;\r\n" +
                             "  }\r\n" +
                             "  @page :first {\r\n" +
                             "    background: #bfbfbf;\r\n" +
                             "    -webkit-print-color-adjust: exact;\r\n" +
                             "    print-color-adjust: exact;\r\n" +
                             "  }\r\n" +
                             "  @page :left {\r\n" +
                             "    background: #bfbfbf;\r\n" +
                             "    -webkit-print-color-adjust: exact;\r\n" +
                             "    print-color-adjust: exact;\r\n" +
                             "  }\r\n" +
                             "  @page :right {\r\n" +
                             "    background: #bfbfbf;\r\n" +
                             "    -webkit-print-color-adjust: exact;\r\n" +
                             "    print-color-adjust: exact;\r\n" +
                             "  }\r\n" +
                             "  html, body {\r\n" +
                             "    background: #bfbfbf !important;\r\n" +
                             "    -webkit-print-color-adjust: exact;\r\n" +
                             "    print-color-adjust: exact;\r\n" +
                             "  }\r\n" +
                             "  * {\r\n" +
                             "    -webkit-print-color-adjust: exact;\r\n" +
                             "    print-color-adjust: exact;\r\n" +
                             "  }\r\n" +
                             "}\r\n" +
                             "</style>\r\n" +
                             "</head>\r\n<body style=\"background: #bfbfbf;\">\r\n" +
                             "<div class=\"container\">\r\n" +
                             "<div class=\"row\">\r\n" +
                             "<div class=\"Col-md-12\">\r\n" +
                             CompanyLogoghtml +
                             /*"<img src=\"" + CompanyLogov1[i] + "\"  width=\"200\" height=\"200\" />\r\n"*/
                             "<p>Company  :" + Companyv1[i] + "</p>\r\n " +
                             "<p>Product  :" + productv1[i] + " </p>\r\n" +
                             "<p>Description</p>\r\n" +
                             "<p>" + ProductDescriptionV1[i] + "</p>\r\n" +
                             ImageHtml +
                             "<p>Detail Specification</p>\r\n" +
                             "<p>" + productSpecificationv1[i] + "</p>\r\n" +
                             "<img src=\"https://modenaconnector.azurewebsites.net/1.png\" width=\"200\" height=\"200\" />\r\n" +
                             "</div>\r\n" +
                             "</div>\r\n" +
                             "</div>\r\n" +
                             "</body>\r\n" +
                             "</html>";

                        ceTe.DynamicPDF.HtmlConverter.Converter.Convert(html1, path + "-" + i + ".pdf", null, options);

                        //Clear The HTML
                        html1 = ClearOfHtml();
                        ImageHtml = ClearOfHtml();
                    }

                    MessageBox.Show("PDF/s Exported Successfully");
                    MessageBox.Show(path);
                }
            }

            clear();
        }

        public string ClearOfHtml()
        {
            return "";
        }

        #region Authenticate


        private static async Task<string> GetAccessToken(string clientId, string tenantId, string redirectUri, string[] scopes)
        {
            var confidentialClientApplication = ConfidentialClientApplicationBuilder
                .Create(clientId)
                .WithClientSecret("****************************************")
                .WithAuthority(new Uri($"https://login.microsoftonline.com/{tenantId}"))  //https://graph.microsoft.com/.default.
                .Build();

            try
            {
                //"api://2622225b-709f-4917-a275-36fa0d67ce17"
                //var lastScope = scopes.Last();
                //var resourceIdentifier = "api://2622225b-709f-4917-a275-36fa0d67ce17"; // Replace with your actual resource identifier

                //if (!lastScope.EndsWith($"/.default"))
                //{
                //    scopes[scopes.Length - 1] = $"{resourceIdentifier}/.default";
                //}

                var authResult = await confidentialClientApplication
                    .AcquireTokenForClient(scopes) // Add "/.default" to the resource identifier
                    .ExecuteAsync();

                return authResult.AccessToken;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error retrieving access token: {ex.Message}");
                return null;
            }
        }

        public async void DownloadFile(string filePath, string fileUrl, string AccessToken)
        {

            // Console.WriteLine($"Access Token: {authResult.AccessToken}");
            // Use the access token to make requests to the Microsoft Graph API

            string MicrosoftglUrl = "https://graph.microsoft.com/v1.0/me";    //  "https://graph.microsoft.com/v1.0/me/drive"; //"https://modenaaec-my.sharepoint.com/personal/sibusiso_modena-aec_co_za/Documents/Pictures/alarmOnHMI.jpg";


            //Method to download image
            string Id = await GetTheId(AccessToken, MicrosoftglUrl, fileUrl, filePath);

            //https://graph.microsoft.com/v1.0/me/drive/root/children

            //GetDriveItemId(authResult.AccessToken);

            //https://graph.microsoft.com/v1.0/me/drive/root/children


            #region old Method

            //var accessToken = await GetAccessToken(clientId, tenantId, redirectUri, scopes);

            //if (!string.IsNullOrEmpty(accessToken))
            //{
            //    //var driveItemId = 
            //    GetDriveItemId(accessToken); 
            //    "https://graph.microsoft.com/v1.0/me/drive/items/EncZjld9M79Ms0cruLVAIhUBz5e4In-ZyVqZOVktLeyseg");
            //    // GetItem(driveItemId, accessToken);
            //}
            //else
            //{
            //    Console.WriteLine("Access token retrieval failed.");
            //}

            #endregion
        }


        private static async System.Threading.Tasks.Task<string> GetTheId(string accessToken, string imageUrl, string fileUrl, string filepath)
        {
            try
            {
                using (var httpClient = new HttpClient())
                {
                    httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                    var response = await httpClient.GetAsync(imageUrl);

                    if (response.IsSuccessStatusCode)
                    {
                        // Your JSON string

                        /// drives /{ remoteItem - driveId}/ items /{ remoteItem - id}/ workbook / worksheets

                        //using (Stream responseStream = await response.Content.ReadAsStreamAsync(),
                        //                   fileStream1 = new FileStream(filePath + "-CompanyLogo-0.jpg", FileMode.Create, FileAccess.Write, FileShare.None))
                        //{
                        //    await responseStream.CopyToAsync(fileStream1);
                        //}

                        //Console.WriteLine(response.StatusCode);

                        //var imageBytes = await response.Content.ReadAsByteArrayAsync();

                        //// Save the image to a file or process it as needed
                        //System.IO.File.WriteAllBytes("downloaded-image.jpg", imageBytes);

                        //Console.WriteLine("Image downloaded successfully.");

                        //var responseData = await response.Content.ReadAsStringAsync();

                        //Console.WriteLine(responseData);

                        //// Deserialize the JSON string to a dynamic object
                        //dynamic obj = JsonConvert.DeserializeObject(responseData);

                        //// Extract the id
                        //string id = obj.id;

                        //Console.WriteLine("The ID is: " + id);

                        //string sharingUrl = "https://modenaaec-my.sharepoint.com/:i:/g/personal/sibusiso_modena-aec_co_za/EaJcMqwb0eNMj2NdqmWQdQcBvclAeirpFJeln9ufJ0RkCQ?e=VVgBQL";
                        string sharingUrl = fileUrl;

                        string encodedUrl = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(sharingUrl)).TrimEnd('=').Replace('/', '_').Replace('+', '-');
                        string requestUrl = $"https://graph.microsoft.com/v1.0/shares/u!{encodedUrl}/driveItem";

                        var httpClient1 = new HttpClient();
                        httpClient1.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
                        var response1 = await httpClient.GetAsync(requestUrl);

                        if (response1.IsSuccessStatusCode)
                        {
                            string responseBody = await response1.Content.ReadAsStringAsync();
                            dynamic driveItem = JsonConvert.DeserializeObject(responseBody);

                            string driveItemId = driveItem.id;
                            Console.WriteLine("Drive Item ID: " + driveItemId);

                            // Download File
                            // Replace with actual Drive Item ID
                            string fileEndpoint = $"https://graph.microsoft.com/v1.0/me/drive/items/{driveItemId}/content";

                            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
                            var fileResponse = await httpClient.GetAsync(fileEndpoint);

                            if (fileResponse.IsSuccessStatusCode)
                            {
                                // var fileContent = await fileResponse.Content.ReadAsStreamAsync();
                                // Save fileContent to a file or process it as needed
                                using (Stream responseStream = await fileResponse.Content.ReadAsStreamAsync(),
                                  fileStream2 = new FileStream(filepath, FileMode.Create, FileAccess.Write, FileShare.None))
                                {
                                    await responseStream.CopyToAsync(fileStream2);
                                }
                            }

                            return "Successful";
                            // Now use the driveItemId to download the file or perform other operations
                        }
                        else
                        {
                            // Handle errors
                            return "error";
                        }
                    }
                    else
                    {
                        //Console.WriteLine($"Error downloading image. Status code: {response.StatusCode}");

                        return response.StatusCode.ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                //Console.WriteLine($"Error downloading image: {ex.Message}");
                return $"Error downloading image: {ex.Message}";
            }
        }

        #endregion
        /// <summary>
        /// Select the image folder
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void button1_Click_1(object sender, EventArgs e)
        {
            //used to be used to select image folder
        }



        private void button2_Click_1(object sender, EventArgs e)
        {
            //used to  be button to create a PDF
        }

        public void clear()
        {
            CompanyName.Clear();

            Brand.Clear();

            Product.Clear();

            ProductDescription.Clear();

            ProductImage.Clear();

            ProductLineDrawing.Clear();

            ProductDetail1.Clear();
        }

        private async void btnExportSeparateFiles_Click(object sender, EventArgs e)
        {
            GetSeparateDocs();

            SaveFileDialog saveFile = new SaveFileDialog();

            // Replace with your application (client) ID
            string clientId = "22a9ad7f-fbca-41cd-9a6f-b766d94318ff";      //"2622225b-709f-4917-a275-36fa0d67ce17";

            // // Replace with your tenant ID
            string tenantId = "7b57e4fd-85fc-4a62-8f79-bfc9313a53a2";      //"819f729d-023e-421e-b387-09d3afd1b668";

            // // Replace with the redirect URI specified in your Azure AD app registration
            string redirectUri = "https://login.microsoftonline.com/common/oauth2/nativeclient"; //https://login.microsoftonline.com/common/oauth2/nativeclient";

            // // Replace with the requested Microsoft Graph API scopes
            string[] scopes = { "files.read", "files.read.all", "files.readwrite", "files.readwrite.all" };

            string authority = "https://login.microsoftonline.com/7b57e4fd-85fc-4a62-8f79-bfc9313a53a2";

            var app = PublicClientApplicationBuilder
           .Create(clientId)
           .WithRedirectUri(redirectUri)
           .WithAuthority(new Uri(authority))
           .Build();

            var accounts = await app.GetAccountsAsync();

            AuthenticationResult authResult = null;

            try
            {
                authResult = await app.AcquireTokenInteractive(scopes)
                    .WithAccount(accounts.FirstOrDefault()) // You can choose the user account if necessary
                    .WithPrompt(Microsoft.Identity.Client.Prompt.SelectAccount) // Prompt for account selection
                    .ExecuteAsync();
            }
            catch (MsalException ex)
            {
                // Handle MFA challenges or other authentication errors
                Console.WriteLine($"MsalException: {ex.Message}");

                // return $"MsalException: {ex.Message}";
            }

            if (authResult != null)
            {

                if (saveFile.ShowDialog() == DialogResult.OK)
                {
                    for (int i = 0; i <= ProductDescription1.Count - 1; i++)
                    {
                        await SeparateTheDocumentsAsync(ProductImage1[i], ProductLineDrawing1[i], ProductimageUrl1[i], ProductimageUrl2[i], ProductimageUrl3[i], ProductDetailSpecification[i], saveFile.FileName + " " + i + " ", authResult.AccessToken);
                        await DownloadPDFAsync(saveFile.FileName + " " + i + " ", Catalogue[i]);
                    }
                }
            }
        }

        public async System.Threading.Tasks.Task DownloadPDFAsync(string saveFile, string link)
        {
            if (link != "")
            {
                Uri F = new Uri(link);

                using (HttpClient client = new HttpClient())
                {
                    var response = await client.GetAsync(F);
                    response.EnsureSuccessStatusCode();

                    using (Stream responseStream = await response.Content.ReadAsStreamAsync(),
                           fileStream = new FileStream(saveFile + ".pdf", FileMode.Create, FileAccess.Write, FileShare.None))
                    {
                        await responseStream.CopyToAsync(fileStream);
                    }

                    await System.Threading.Tasks.Task.Delay(TimeSpan.FromSeconds(1));
                }
            }
        }


        public async System.Threading.Tasks.Task SeparateTheDocumentsAsync(string imglink, string lineDrawingLink, string imglink2, string imglink3, string imglink4, string ProductDescription, string saveFile, string Auth)
        {
            try
            {
                string extention = "";

                extention = ".jpg";

                if (imglink != "")
                {
                    //Uri A = new Uri(imglink);

                    //using (HttpClient client = new HttpClient())
                    //{
                    //    var response = await client.GetAsync(A);
                    //    response.EnsureSuccessStatusCode();

                    //    using (Stream responseStream = await response.Content.ReadAsStreamAsync(),
                    //           fileStream = new FileStream(saveFile + "-Product-Image-A" + extention, FileMode.Create, FileAccess.Write, FileShare.None))
                    //    {
                    //        await responseStream.CopyToAsync(fileStream);
                    //    }
                    //}

                    DownloadFile(saveFile + "-Product-Image-A" + extention, imglink, Auth);
                }

                await System.Threading.Tasks.Task.Delay(TimeSpan.FromSeconds(1));

                if (imglink2 != "")
                {

                    //Uri B = new Uri(imglink2);

                    //using (HttpClient client = new HttpClient())
                    //{
                    //    var response = await client.GetAsync(B);
                    //    response.EnsureSuccessStatusCode();

                    //    using (Stream responseStream = await response.Content.ReadAsStreamAsync(),
                    //           fileStream = new FileStream(saveFile + "-Product-Image-B" + extention, FileMode.Create, FileAccess.Write, FileShare.None))
                    //    {
                    //        await responseStream.CopyToAsync(fileStream);
                    //    }
                    //}

                    DownloadFile(saveFile + "-Product-Image-B" + extention, imglink2, Auth);
                }

                await System.Threading.Tasks.Task.Delay(TimeSpan.FromSeconds(1));

                if (imglink3 != "")
                {

                    //Uri C = new Uri(imglink3);

                    //using (HttpClient client = new HttpClient())
                    //{
                    //    var response = await client.GetAsync(C);
                    //    response.EnsureSuccessStatusCode();

                    //    using (Stream responseStream = await response.Content.ReadAsStreamAsync(),
                    //           fileStream = new FileStream(saveFile + "-Product-Image-C" + extention, FileMode.Create, FileAccess.Write, FileShare.None))
                    //    {
                    //        await responseStream.CopyToAsync(fileStream);
                    //    }
                    //}

                    DownloadFile(saveFile + "-Product-Image-C" + extention, imglink3, Auth);

                }

                await System.Threading.Tasks.Task.Delay(TimeSpan.FromSeconds(1));

                if (imglink4 != "")
                {

                    //Uri D = new Uri(imglink4);

                    //using (HttpClient client = new HttpClient())
                    //{
                    //    var response = await client.GetAsync(D);
                    //    response.EnsureSuccessStatusCode();

                    //    using (Stream responseStream = await response.Content.ReadAsStreamAsync(),
                    //           fileStream = new FileStream(saveFile + "-Product-Image-D" + extention, FileMode.Create, FileAccess.Write, FileShare.None))
                    //    {
                    //        await responseStream.CopyToAsync(fileStream);
                    //    }
                    //}

                    DownloadFile(saveFile + "-Product-Image-D" + extention, imglink4, Auth);
                }

                await System.Threading.Tasks.Task.Delay(TimeSpan.FromSeconds(1));

                if (lineDrawingLink != "")
                {

                    //Uri E = new Uri(lineDrawingLink);

                    //using (HttpClient client = new HttpClient())
                    //{
                    //    var response = await client.GetAsync(E);
                    //    response.EnsureSuccessStatusCode();

                    //    using (Stream responseStream = await response.Content.ReadAsStreamAsync(),
                    //           fileStream = new FileStream(saveFile + "-Line-Drawing" + extention, FileMode.Create, FileAccess.Write, FileShare.None))
                    //    {
                    //        await responseStream.CopyToAsync(fileStream);
                    //    }
                    //}

                    DownloadFile(saveFile + "-Line-Drawing" + extention, lineDrawingLink, Auth);

                    //used to save the Line Drawing image
                    // img1.Save(saveFile + "LineDrawing" + extention);       //saveFile.FileName + "LineDrawing" + extention);
                    //}
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("An Error has Occured " + ex);
            }
        }

        private async void button1_Click_2(object sender, EventArgs e)
        {


        }

        public void ReadNewFormat()
        {
            OpenFileDialog Openfile = new OpenFileDialog();

            string filePath = @"C:\\Users\\<USER>\\Downloads\\Insight blueprint\\Financial Sample.xlsx";
            string ExcelData = "";
            int DrCount = 0;
            int fileCount = 0;

            //Open the file
            if (Openfile.ShowDialog() == DialogResult.OK)
            {
                filePath = Openfile.FileName;
                GlobalFilePath = filePath;
                lbFileName.Text = filePath;

                MessageBox.Show("The File You Selected Is " + filePath);

                MessageBox.Show("Please Select Where You Would Like To Export Your Files");

                string con = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + filePath + ";Extended Properties=\"Excel 12.0;HDR=YES;IMEX=1\";";

                using (OleDbConnection connection = new OleDbConnection(con))
                {
                    connection.Open();

                    OleDbCommand command = new OleDbCommand("select * from [Sheet1$]", connection); //[Sheet1$]

                    using (OleDbDataReader dr = command.ExecuteReader())
                    {
                        fileCount = dr.FieldCount;

                        while (dr.Read())
                        {
                            DrCount++;

                            //Secound Column
                            if (dr[0].ToString() != "")
                            {
                                Companyv1.Add(dr?[0].ToString());
                                CompanyLogov1.Add(dr?[1].ToString());
                                productv1.Add(dr?[2].ToString());
                                ProductDescriptionV1.Add(dr?[3].ToString());
                                productSpecificationv1.Add(dr?[4].ToString());
                                ProductCodev1.Add(dr?[5].ToString());
                                ProductimageUrl.Add(dr?[6].ToString());
                                productTechnicalUrl.Add(dr?[8].ToString());
                                ProductimageUrl1.Add(dr?[9].ToString());
                                ProductimageUrl2.Add(dr?[10].ToString());
                                ProductimageUrl3.Add(dr?[11].ToString());
                                ProductimageUrl3.Add(dr?[12].ToString());
                                Catalogue.Add(dr?[13].ToString());
                            }
                        }
                    }

                    connection.Close();
                }
            }
            else if (Openfile.ShowDialog() == DialogResult.Cancel)
            {
                MessageBox.Show("The operation has been Cancelled");
            }
        }

        private void label3_Click(object sender, EventArgs e)
        {

        }
    }
}