﻿using Microsoft.Identity.Client;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIM4U.Authentication
{
    public class OneDriveAuthenticator
    {
        public async Task<string> AuthenticateUserAndReturnAccessToken()
        {
            //Application (client) ID
            string clientId = "67ca058c-351f-4d91-a1b7-4637dcdfbeb5"; //"22a9ad7f-fbca-41cd-9a6f-b766d94318ff"; 

            //Tenant ID
            string tenantId = "193daea9-73e0-467d-a9c8-2cf78c9485ba"; //"7b57e4fd-85fc-4a62-8f79-bfc9313a53a2"; 

            //The redirect URI specified in your Azure AD app registration
            string redirectUri = "https://login.microsoftonline.com/common/oauth2/nativeclient"; //https://login.microsoftonline.com/common/oauth2/nativeclient";

            //The requested Microsoft Graph API scopes
            string[] scopes = { "files.read", "files.read.all", "files.readwrite", "files.readwrite.all" };

            string authority = "https://login.microsoftonline.com/193daea9-73e0-467d-a9c8-2cf78c9485ba"; //7b57e4fd-85fc-4a62-8f79-bfc9313a53a2";                                  

            var app = PublicClientApplicationBuilder
           .Create(clientId)
           .WithRedirectUri(redirectUri)
           .WithAuthority(new Uri(authority))
           .Build();

            var accounts = await app.GetAccountsAsync();

            AuthenticationResult authResult = null;

            try
            {
                authResult = await app.AcquireTokenInteractive(scopes)
                    .WithAccount(accounts.FirstOrDefault()) 
                    .WithPrompt(Microsoft.Identity.Client.Prompt.SelectAccount) 
                    .ExecuteAsync();
            }
            catch (MsalException ex)
            {
                // Handle MFA challenges or other authentication errors
                Console.WriteLine($"MsalException: {ex.Message}");

                // return $"MsalException: {ex.Message}";
            }

            if (authResult != null)
            {
                return authResult.AccessToken;
            }

            return "";
        }
    }
}
