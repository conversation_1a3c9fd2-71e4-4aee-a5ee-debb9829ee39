﻿namespace BIM4U
{
    partial class frmMain
    {
        /// <summary>
        ///  Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        ///  Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        ///  Required method for Designer support - do not modify
        ///  the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            btnExportSingleDocument = new Button();
            label1 = new Label();
            btnExportSeparateFiles = new Button();
            lbCopyRight = new Label();
            label2 = new Label();
            chkIsOneDriveLinks = new CheckBox();
            btnSelectFolder = new Button();
            txtSelectFolder = new TextBox();
            label3 = new Label();
            label4 = new Label();
            txtSelectFile = new TextBox();
            btnSelectFile = new Button();
            lblProgress = new Label();
            SuspendLayout();
            // 
            // btnExportSingleDocument
            // 
            btnExportSingleDocument.Enabled = false;
            btnExportSingleDocument.Location = new System.Drawing.Point(103, 377);
            btnExportSingleDocument.Margin = new Padding(3, 2, 3, 2);
            btnExportSingleDocument.Name = "btnExportSingleDocument";
            btnExportSingleDocument.Size = new System.Drawing.Size(312, 48);
            btnExportSingleDocument.TabIndex = 0;
            btnExportSingleDocument.Text = "Export As Single Document";
            btnExportSingleDocument.UseVisualStyleBackColor = true;
            btnExportSingleDocument.Click += btnExportSingleDocument_Click;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new System.Drawing.Point(87, 285);
            label1.Name = "label1";
            label1.Size = new System.Drawing.Size(341, 15);
            label1.TabIndex = 1;
            label1.Text = "Please select options to export the excel document information";
            // 
            // btnExportSeparateFiles
            // 
            btnExportSeparateFiles.Enabled = false;
            btnExportSeparateFiles.Location = new System.Drawing.Point(103, 446);
            btnExportSeparateFiles.Margin = new Padding(3, 2, 3, 2);
            btnExportSeparateFiles.Name = "btnExportSeparateFiles";
            btnExportSeparateFiles.Size = new System.Drawing.Size(312, 48);
            btnExportSeparateFiles.TabIndex = 5;
            btnExportSeparateFiles.Text = "Export As Separate Files";
            btnExportSeparateFiles.UseVisualStyleBackColor = true;
            btnExportSeparateFiles.Click += btnExportSeparateFiles_Click;
            // 
            // lbCopyRight
            // 
            lbCopyRight.AutoSize = true;
            lbCopyRight.Location = new System.Drawing.Point(123, 507);
            lbCopyRight.Name = "lbCopyRight";
            lbCopyRight.Size = new System.Drawing.Size(236, 15);
            lbCopyRight.TabIndex = 6;
            lbCopyRight.Text = "Copyright @2023 Bim4U All rights Reserved";
            lbCopyRight.TextAlign = ContentAlignment.TopCenter;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Location = new System.Drawing.Point(136, 548);
            label2.Name = "label2";
            label2.Size = new System.Drawing.Size(187, 15);
            label2.TabIndex = 7;
            label2.Text = "This application belongs to Bim4U";
            label2.TextAlign = ContentAlignment.TopCenter;
            // 
            // chkIsOneDriveLinks
            // 
            chkIsOneDriveLinks.AutoSize = true;
            chkIsOneDriveLinks.Checked = true;
            chkIsOneDriveLinks.CheckState = CheckState.Checked;
            chkIsOneDriveLinks.Enabled = false;
            chkIsOneDriveLinks.Location = new System.Drawing.Point(190, 335);
            chkIsOneDriveLinks.Margin = new Padding(3, 2, 3, 2);
            chkIsOneDriveLinks.Name = "chkIsOneDriveLinks";
            chkIsOneDriveLinks.Size = new System.Drawing.Size(131, 19);
            chkIsOneDriveLinks.TabIndex = 12;
            chkIsOneDriveLinks.Text = "Is it One Drive links?";
            chkIsOneDriveLinks.UseVisualStyleBackColor = true;
            // 
            // btnSelectFolder
            // 
            btnSelectFolder.Location = new System.Drawing.Point(77, 57);
            btnSelectFolder.Name = "btnSelectFolder";
            btnSelectFolder.Size = new System.Drawing.Size(63, 23);
            btnSelectFolder.TabIndex = 13;
            btnSelectFolder.Text = "Browse";
            btnSelectFolder.UseVisualStyleBackColor = true;
            btnSelectFolder.Click += btnSelectFolder_Click;
            // 
            // txtSelectFolder
            // 
            txtSelectFolder.Location = new System.Drawing.Point(146, 58);
            txtSelectFolder.Name = "txtSelectFolder";
            txtSelectFolder.Size = new System.Drawing.Size(269, 23);
            txtSelectFolder.TabIndex = 14;
            txtSelectFolder.TextChanged += txtSelectFolder_TextChanged;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Location = new System.Drawing.Point(146, 40);
            label3.Name = "label3";
            label3.Size = new System.Drawing.Size(162, 15);
            label3.TabIndex = 15;
            label3.Text = "Select a folder to save all data";
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Location = new System.Drawing.Point(146, 96);
            label4.Name = "label4";
            label4.Size = new System.Drawing.Size(182, 15);
            label4.TabIndex = 18;
            label4.Text = "Select the source Excel workbook";
            // 
            // txtSelectFile
            // 
            txtSelectFile.Location = new System.Drawing.Point(146, 114);
            txtSelectFile.Name = "txtSelectFile";
            txtSelectFile.Size = new System.Drawing.Size(269, 23);
            txtSelectFile.TabIndex = 17;
            txtSelectFile.TextChanged += txtSelectFile_TextChanged;
            // 
            // btnSelectFile
            // 
            btnSelectFile.Location = new System.Drawing.Point(77, 113);
            btnSelectFile.Name = "btnSelectFile";
            btnSelectFile.Size = new System.Drawing.Size(63, 23);
            btnSelectFile.TabIndex = 16;
            btnSelectFile.Text = "Browse";
            btnSelectFile.UseVisualStyleBackColor = true;
            btnSelectFile.Click += btnSelectFile_Click;
            // 
            // lblProgress
            // 
            lblProgress.AutoSize = true;
            lblProgress.Location = new System.Drawing.Point(87, 160);
            lblProgress.Name = "lblProgress";
            lblProgress.Size = new System.Drawing.Size(91, 15);
            lblProgress.TabIndex = 19;
            lblProgress.Text = "{Progress Label}";
            // 
            // frmMain
            // 
            AutoScaleDimensions = new System.Drawing.SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new System.Drawing.Size(504, 602);
            Controls.Add(lblProgress);
            Controls.Add(label4);
            Controls.Add(txtSelectFile);
            Controls.Add(btnSelectFile);
            Controls.Add(label3);
            Controls.Add(txtSelectFolder);
            Controls.Add(btnSelectFolder);
            Controls.Add(chkIsOneDriveLinks);
            Controls.Add(label2);
            Controls.Add(lbCopyRight);
            Controls.Add(btnExportSeparateFiles);
            Controls.Add(label1);
            Controls.Add(btnExportSingleDocument);
            FormBorderStyle = FormBorderStyle.FixedSingle;
            Margin = new Padding(3, 2, 3, 2);
            MaximizeBox = false;
            Name = "frmMain";
            Text = "BIM 4 U";
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private Button btnExportSingleDocument;
        private Label label1;
        private Button btnExportSeparateFiles;
        private Label lbCopyRight;
        private Label label2;
        private CheckBox chkIsOneDriveLinks;
        private Button btnSelectFolder;
        private TextBox txtSelectFolder;
        private Label label3;
        private Label label4;
        private TextBox txtSelectFile;
        private Button btnSelectFile;
        private Label lblProgress;
    }
}