﻿using BIM4U.Authentication;
using Microsoft.Identity.Client;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace BIM4U
{
    public partial class frmMain : Form
    {
        private readonly string ProgressLabelDefault = "Please select a folder and file to begin processing...";
        private readonly string UnhandledErrorMessage = "Unhandled error occurred. Please contact support.";
        B4UProcessor? _processor;
        OneDriveAuthenticator _oneDriveAuth;
        private string _AccessToken = null;
        public frmMain()
        {
            InitializeComponent();
            ResetProgressLabel();
            _oneDriveAuth = new OneDriveAuthenticator();
        }

        private void ResetProgressLabel()
        {
            lblProgress.Text = ProgressLabelDefault;
        }

        private void ResetInputs()
        {
            ResetProgressLabel();

            txtSelectFile.Text = "";
            txtSelectFolder.Text = "";
        }

        private void DisableButtons()
        {
            btnExportSeparateFiles.Enabled = false;
            btnExportSingleDocument.Enabled = false;
        }

        private void EnableButtons()
        {
            btnExportSeparateFiles.Enabled = true;
            btnExportSingleDocument.Enabled = true;
        }

        private async void btnExportSingleDocument_Click(object sender, EventArgs e)
        {
            try
            {
                DisableButtons();
                await SetupProcessor();
                await _processor.ProcessSingleParallelAsync();
                _processor_OnProgressUpdate("Process complete.");
            }
            catch (Exception ex)
            {
                string message = UnhandledErrorMessage + Environment.NewLine + ex.Message;
                MessageBox.Show(message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                EnableButtons();
                RefreshButtonStatuses();
                TearDownProcessor();
            }
        }

        /// <summary>
        /// Sets up a new instance of the _processor with the selected folder and file and subscribes to events.
        /// </summary>
        private async Task SetupProcessor()
        {
            if (_processor != null)
            {
                TearDownProcessor();
            }

            if (chkIsOneDriveLinks.Checked && string.IsNullOrEmpty(_AccessToken))
            {
                _processor_OnProgressUpdate("Authenticating...");
                try
                {
                    var res = await AuthenticateOneDrive();
                    if (!res)
                    {
                        _processor_OnProgressUpdate("Authentication failed.");
                        throw new Exception("Authentication Failed.");
                    }
                }
                catch (Exception)
                {
                    throw;
                }
            }

            _processor = new B4UProcessor(txtSelectFolder.Text, txtSelectFile.Text, _AccessToken);
            _processor.OnProgressUpdate += _processor_OnProgressUpdate;
        }

        private async Task<bool> AuthenticateOneDrive()
        {
            try
            {
                _AccessToken = await _oneDriveAuth.AuthenticateUserAndReturnAccessToken();
                return !string.IsNullOrEmpty(_AccessToken);
            }
            catch (Exception)
            {
                throw;
            }
        }

        private void _processor_OnProgressUpdate(string message)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(_processor_OnProgressUpdate, message);
                return;
            }
            lblProgress.Text = message;
            lblProgress.Refresh();
        }

        
        /// <summary>
        /// Disposes of the _processor.
        /// </summary>
        private void TearDownProcessor()
        {
        tryagain:
            try
            {
                if (_processor != null)
                {
                    _processor.OnProgressUpdate -= _processor_OnProgressUpdate;
                    _processor.Dispose();
                    _processor = null;
                }
            }
            catch (Exception ex)
            {
                string msg = "Please ensure that all log files have been closed." + Environment.NewLine + ex.Message;
                MessageBox.Show(msg, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                goto tryagain;
            }
        }

        private async void btnExportSeparateFiles_Click(object sender, EventArgs e)
        {
            try
            {
                DisableButtons();
                await SetupProcessor();
                await _processor.ProcessMultipleParallelAsync();
                _processor_OnProgressUpdate("Process complete.");
            }
            catch (Exception ex)
            {
                string message = UnhandledErrorMessage + Environment.NewLine + ex.Message;
                MessageBox.Show(message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                EnableButtons();
                RefreshButtonStatuses();
                TearDownProcessor();
            }
        }

        private void txtSelectFolder_TextChanged(object sender, EventArgs e)
        {
            RefreshButtonStatuses();
        }

        private void txtSelectFile_TextChanged(object sender, EventArgs e)
        {
            RefreshButtonStatuses();
        }

        /// <summary>
        /// Checks that the selected folder and file are valid and enables/disables the buttons.
        /// </summary>
        private void RefreshButtonStatuses()
        {
            var dir = txtSelectFolder.Text;
            var file = txtSelectFile.Text;

            //Check that both the dir and file are populated and exists and enable buttons.
            bool validDir = !string.IsNullOrEmpty(dir) && Directory.Exists(dir);
            bool validFile = !string.IsNullOrEmpty(file) && File.Exists(file);

            bool allValid = validDir == true && validFile == true;
            chkIsOneDriveLinks.Enabled = allValid;
            btnExportSingleDocument.Enabled = allValid;
            btnExportSeparateFiles.Enabled = allValid;
        }

        private void btnSelectFolder_Click(object sender, EventArgs e)
        {
            using (FolderBrowserDialog fd = new FolderBrowserDialog())
            {
                if (fd.ShowDialog() == DialogResult.OK)
                {
                    txtSelectFolder.Text = fd.SelectedPath;
                }
            }
        }

        private void btnSelectFile_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog fd = new OpenFileDialog())
            {
                //add a filter for all excel file extensions.
                fd.Filter = "Excel Files|*.xls;*.xlsx;*.xlsm";
                if (fd.ShowDialog() == DialogResult.OK)
                {
                    txtSelectFile.Text = fd.FileName;
                }
            }
        }
    }
}
