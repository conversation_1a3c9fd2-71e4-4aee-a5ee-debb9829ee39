﻿using BIM4U.Authentication;
using BIM4U.Data;
using BIM4U.Exporters;
using BIM4U.Extensions;
using BIM4U.Import;
using BIM4U.Loggers;
using DevExpress.Spreadsheet;
using DevExpress.XtraSpreadsheet;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.ServiceModel;
using System.Text;
using System.Threading.Tasks;
using System.Collections.Specialized;

namespace BIM4U
{
    /// <summary>
    /// The main class to process excel data.
    /// </summary>
    public class B4UProcessor : IDisposable
    {
        #region EventsAndDelegates
        public delegate void ProgressUpdateDelegate(string message);
        public event ProgressUpdateDelegate? OnProgressUpdate;
        #endregion
        #region FoldersAndFiles
        public string RootFolder { get; private set; }
        public string Folder_UncompressedImages
        {
            get
            {
                return Path.Combine(RootFolder, "UncompressedImages//");
            }
        }

        public string Folder_CompressedImages
        {
            get
            {
                return Path.Combine(RootFolder, "CompressedImages//");
            }
        }

        public string Folder_SingleDocsExport
        {
            get
            {
                return Path.Combine(RootFolder, "SingleDocsExport//");
            }
        }

        public string Folder_MultipleDocsExport
        {
            get
            {
                return Path.Combine(RootFolder, "MultipleDocsExport//");
            }
        }

        public string File_ErrorLog
        {
            get
            {
                return Path.Combine(RootFolder, "ErrorLog.txt");
            }
        }

        public string File_SpreadsheetErrorLog
        {
            get
            {
                return Path.Combine(RootFolder, "SpreadsheetErrorLog.xlsx");
            }
        }

        public string File_SourceSpreadsheet { get; private set; }
        #endregion

        SpreadSheetErrorLogger _spcErrorLog;
        TextErrorLogger _textErrorLog;

        ExcelDataImporter _importer;
        ImageDownloadManager _imageDownloadManager;

        SinglePDfDocumentExporter _singlePDfDocumentExporter;
        MulitplePDFDocumentExporter _mulitplePDFDocumentExporter;

        Workbook _workbook;
        Worksheet _sourceWorkSheet { get { return _workbook.Worksheets[0]; } }

        private int _threadCount = 4;
        /// <summary>
        /// Sets the amount of threads to be used for parallel download and export operations.
        /// </summary>
        public int ThreadCount
        {
            get
            {
                return _threadCount;
            }

            set
            {
                if(value <= 0)
                {
                    value = 1;
                }

                _threadCount = value;
            }
        }


        //DirectLinkImageDownloader _directLinkImageDownloader;
        //OneDriveAuthenticator oneDriveAuthenticator;
        //OneDriveImageDownloader _oneDriveImageDownloader;

        public B4UProcessor(string rootFolder, string sourceSpreadsheet, string oneDriveAccessToken)
        {
            if (string.IsNullOrEmpty(rootFolder))
            {
                throw new ArgumentNullException(nameof(rootFolder));
            }

            if (!Directory.Exists(rootFolder))
            {
                throw new DirectoryNotFoundException(rootFolder);
            }

            if (string.IsNullOrEmpty(sourceSpreadsheet))
            {
                throw new Exception("Missing required argument '" + nameof(sourceSpreadsheet) + ".");
            }
            else if(!File.Exists(sourceSpreadsheet))
            {
                throw new FileNotFoundException(sourceSpreadsheet);
            }
            RootFolder = rootFolder;
            CreateSubFolders();


            File_SourceSpreadsheet = sourceSpreadsheet;
            //Copy the source spreadsheet to the root folder
            File_SourceSpreadsheet = CopySourceSheetToRootFolder(sourceSpreadsheet);
            _workbook = new Workbook();

            _workbook.LoadDocument(sourceSpreadsheet);
            

            _spcErrorLog = new SpreadSheetErrorLogger(File_SpreadsheetErrorLog);
            _textErrorLog = new TextErrorLogger(File_ErrorLog);

            _importer = new ExcelDataImporter();
            _importer.OnError += OnError;

            _imageDownloadManager = new ImageDownloadManager(Folder_CompressedImages, Folder_UncompressedImages, oneDriveAccessToken);
            _imageDownloadManager.OnError += OnError;

            _singlePDfDocumentExporter = new SinglePDfDocumentExporter();
            _singlePDfDocumentExporter.OnError += OnError;

            _mulitplePDFDocumentExporter = new MulitplePDFDocumentExporter();
            _mulitplePDFDocumentExporter.OnError += OnError;
        }

        private string CopySourceSheetToRootFolder(string sourceFile)
        {
            var destFileName = Path.GetFileName(sourceFile);
            var destFilePath = Path.Combine(RootFolder, destFileName);
            if (sourceFile != destFilePath)
            {
                File.Copy(sourceFile, destFilePath, true);
            }
            return destFilePath;
        }

        /// <summary>
        /// Creates the sub folder structure.
        /// </summary>
        private void CreateSubFolders()
        {
            Directory.CreateDirectory(Folder_MultipleDocsExport);
            Directory.CreateDirectory(Folder_SingleDocsExport);
            Directory.CreateDirectory(Folder_CompressedImages);
            Directory.CreateDirectory(Folder_UncompressedImages);
        }

        private void OnError(int rowIndex, string message)
        {
            _spcErrorLog.LogError(_sourceWorkSheet, rowIndex, message);
            _textErrorLog.LogError(message);
        }

        public async Task<List<ExcelData>> ImportDataFromExcel()
        {
            return await _importer.GetListOfExcelDataRecordsFromFile(_sourceWorkSheet, Importer_ProgressUpdate);
        }

        //[Obsolete("This method is obsolete, use ProcessSingleParallelAsync instead.", true)]
        //public async Task ProcessSingleAsync()
        //{
        //    //Get data from excel
        //    var excelData = await ImportDataFromExcel();
            
        //    UpdateProgress("Process", "Downloading and verifying images...");

        //    int index = 0;
        //    int failedCount = 0;
        //    //Process urls
        //    List<ExcelData> toProcess = new List<ExcelData>(excelData);
        //    List<ExcelData> successData = new List<ExcelData>();
        //    foreach (var item in toProcess)
        //    {
        //        index++;
        //        UpdateProgress("Downloading Images", $"Processing: {index} of {toProcess.Count} | Failed: {failedCount}");
        //        bool success = await _imageDownloadManager.ProcessImageUrls(item);
        //        if (!success)
        //        {
        //            failedCount++;
        //            UpdateProgress("Downloading Images", $"Processing: {index} of {toProcess.Count} | Failed: {failedCount}");
        //        }
        //        else
        //        {
        //            successData.Add(item);
        //        }
        //    }


        //    toProcess = new List<ExcelData>(successData);
        //    successData.Clear();

        //    //reset index;
        //    index = 0;
        //    failedCount = 0;

        //    foreach (var item in toProcess)
        //    {
        //        index++;
        //        UpdateProgress("Exporting PDF", $"Processing: {index} of {toProcess.Count} | Failed: {failedCount}");
        //        string exportPath = GetPDFExportFilePath( Folder_SingleDocsExport, item.ProductName); //Path.Combine(Folder_SingleDocsExport, item.ProductName + ".pdf");
                
        //        bool success = await _singlePDfDocumentExporter.ReadDataAndExportSinglePDFDocumentAsync(item, exportPath);
        //        if (!success)
        //        {
        //            failedCount++;
        //            UpdateProgress("Exporting PDF", $"Processing: {index} of {toProcess.Count} | Failed: {failedCount}");
        //        }
        //        else
        //        {
        //            successData.Add(item);
        //        }
        //    }
            
            
            
        //    //await Parallel.ForEachAsync(excelData, new ParallelOptions() { MaxDegreeOfParallelism = 1 }, async (data, ct) =>
        //    //{
        //    //    await _imageDownloadManager.ProcessImageUrls(data);
        //    //});

        //    //UpdateProgress("Process", "Exporting PDF docs...");
        //    ////
        //    //await Parallel.ForEachAsync(excelData, new ParallelOptions() { MaxDegreeOfParallelism = 1 }, async (data, ct) =>
        //    //{
        //    //    string exportPath = Path.Combine(Folder_SingleDocsExport, data.ProductName + ".pdf");
        //    //    await _singlePDfDocumentExporter.ReadDataAndExportSinglePDFDocumentAsync(data, exportPath);
        //    //});
        //}

        /// <summary>
        /// Get the pdf file path including extension - checks for existing files in folder and renames file.
        /// </summary>
        /// <param name="folder"></param>
        /// <param name="productName"></param>
        /// <returns></returns>
        private string GetPDFExportFilePath(ConcurrentBag<string> existingFiles, string folder, string productName)
        {
            string fileName = System.IO.Path.Combine(folder, productName.AsFileSafeString());
            if (!fileName.ToLower().Contains(".pdf"))
            {
                fileName += ".pdf";
            }

            string name = fileName;
            int index = 0;
        Tryagain:
            string path = Path.Combine(folder, name);
            //Check for existing file.
            if (File.Exists(path) || existingFiles.Contains(Path.GetFileName(name)))
            {
                index++;
                name = string.Format("{0} ({1}){2}", Path.GetFileNameWithoutExtension(fileName), index, Path.GetExtension(fileName));
                goto Tryagain;
            }
            fileName = name;
            return Path.Combine(folder, fileName);
        }

        /// <summary>
        /// Processes the single export report.
        /// </summary>
        /// <returns></returns>
        public async Task ProcessSingleParallelAsync()
        {
            //Get data from excel
            var excelData = await ImportDataFromExcel();

            UpdateProgress("Process", "Downloading and verifying images...");
            //Set number of threads
            int index = 0;
            int failedCount = 0;
            object _processLock = new object();

            //Process urls
            var toProcess = new ConcurrentBag<ExcelData>(excelData);
            var successData = new ConcurrentBag<ExcelData>();

            await Parallel.ForEachAsync(toProcess, new ParallelOptions() { MaxDegreeOfParallelism = ThreadCount }, async (data, ct) =>
            {
                index++;
                UpdateProgress("Downloading Images", $"Processing: {index} of {toProcess.Count} | Failed: {failedCount}");
                bool success = await _imageDownloadManager.ProcessImageUrls(data);
                if (!success)
                {
                    failedCount++;
                    UpdateProgress("Downloading Images", $"Processing: {index} of {toProcess.Count} | Failed: {failedCount}");
                }
                else
                {
                    successData.Add(data);
                }
            });


            toProcess = new ConcurrentBag<ExcelData>(successData);
            successData.Clear();

            //reset index;
            index = 0;
            failedCount = 0;

            UpdateProgress("Process", "Exporting PDF docs...");
            ConcurrentBag<string> ExistingFiles = GetExistingFileNames(Folder_SingleDocsExport);
            await Parallel.ForEachAsync(toProcess, new ParallelOptions() { MaxDegreeOfParallelism = ThreadCount }, async (data, ct) =>
            {
                index++;
                UpdateProgress("Exporting PDF", $"Processing: {index} of {toProcess.Count} | Failed: {failedCount}");
                string exportPath;// GetPDFExportFilePath(Folder_SingleDocsExport, data.ProductName); //Path.Combine(Folder_SingleDocsExport, data.ProductDescription + ".pdf");
                lock (_processLock)
                {
                    exportPath = GetPDFExportFilePath(ExistingFiles, Folder_SingleDocsExport, data.ProductName);
                    ExistingFiles.Add(Path.GetFileName(exportPath));
                }
                bool success = await _singlePDfDocumentExporter.ReadDataAndExportSinglePDFDocumentAsync(data, exportPath);
                if (!success)
                {
                    failedCount++;
                    UpdateProgress("Exporting PDF", $"Processing: {index} of {toProcess.Count} | Failed: {failedCount}");
                }
                else
                {
                    successData.Add(data);
                }
            });

            //Any additional tasks needs to use SuccessData and clear the toprocess collection.

        }

        private ConcurrentBag<string> GetExistingFileNames(string folder)
        {
            if (Directory.Exists(folder))
            {
                ConcurrentBag<string> toreturn = new ConcurrentBag<string>();
                foreach (var item in Directory.GetFiles(folder))
                {
                    toreturn.Add(Path.GetFileName(item));
                }
                return toreturn;
            }
            else
            {
                throw new DirectoryNotFoundException(folder);
            }
        }

        //[Obsolete("This method is obsolete, use ProcessMultipleParallelAsync instead.", true)]
        //public async Task ProcessMultipleAsync()
        //{
        //    //Get data from excel
        //    var excelData = await ImportDataFromExcel();

        //    UpdateProgress("Process", "Downloading and verifying images...");

        //    int index = 0;
        //    int failedCount = 0;
        //    //Process urls
        //    List<ExcelData> toProcess = new List<ExcelData>(excelData);
        //    List<ExcelData> successData = new List<ExcelData>();
        //    foreach (var item in toProcess)
        //    {
        //        index++;
        //        UpdateProgress("Downloading Images", $"Processing: {index} of {toProcess.Count} | Failed: {failedCount}");
        //        var res = await _imageDownloadManager.ProcessImageUrls(item);
        //        if (!res)
        //        {
        //            failedCount++;
        //            UpdateProgress("Downloading Images", $"Processing: {index} of {toProcess.Count} | Failed: {failedCount}");
        //        }
        //        else
        //        {
        //            successData.Add(item);
        //        }
        //    }


        //    toProcess = new List<ExcelData>(successData);
        //    successData.Clear();

        //    //reset index;
        //    index = 0;
        //    failedCount = 0;

        //    foreach (var item in toProcess)
        //    {
        //        index++;
        //        UpdateProgress("Exporting PDF", $"Processing: {index} of {toProcess.Count} | Failed: {failedCount}");
        //        string exportPath = GetPDFExportFilePath(Folder_MultipleDocsExport, item.ProductName); //Path.Combine(Folder_MultipleDocsExport, item.ProductName + ".pdf");
        //        var res = await _mulitplePDFDocumentExporter.ReadDataAndMulitplePDFDocumentAsync(item, exportPath);
        //        if (!res)
        //        {
        //            failedCount++;
        //            UpdateProgress("Exporting PDF", $"Processing: {index} of {toProcess.Count} | Failed: {failedCount}");
        //        }
        //        else
        //        {
        //            successData.Add(item);
        //        }
        //    }
        //}

        /// <summary>
        /// Processes the multiple export report.
        /// </summary>
        /// <returns></returns>
        public async Task ProcessMultipleParallelAsync()
        {
            //Get data from excel
            var excelData = await ImportDataFromExcel();

            UpdateProgress("Process", "Downloading and verifying images...");

            int index = 0;
            int failedCount = 0;
            object _processLock = new object();
            //Process urls
            ConcurrentBag<ExcelData> toProcess = new ConcurrentBag<ExcelData>(excelData);
            ConcurrentBag<ExcelData> successData = new ConcurrentBag<ExcelData>();
            await Parallel.ForEachAsync(toProcess, new ParallelOptions() { MaxDegreeOfParallelism = ThreadCount }, async (data, ct) =>
            {
                index++;
                UpdateProgress("Downloading Images", $"Processing: {index} of {toProcess.Count} | Failed: {failedCount}");
                bool success = await _imageDownloadManager.ProcessImageUrls(data);
                if (!success)
                {
                    failedCount++;
                    UpdateProgress("Downloading Images", $"Processing: {index} of {toProcess.Count} | Failed: {failedCount}");
                }
                else
                {
                    successData.Add(data);
                }
            });

            toProcess = new ConcurrentBag<ExcelData>(successData);
            successData.Clear();

            //reset index;
            index = 0;
            failedCount = 0;
            ConcurrentBag<string> ExistingFiles = GetExistingFileNames(Folder_MultipleDocsExport);
            await Parallel.ForEachAsync(toProcess, new ParallelOptions() { MaxDegreeOfParallelism = ThreadCount }, async (data, ct) =>
            {
                index++;
                UpdateProgress("Exporting PDF", $"Processing: {index} of {toProcess.Count} | Failed: {failedCount}");
                string exportPath;// = GetPDFExportFilePath(Folder_MultipleDocsExport, data.ProductName); //Path.Combine(Folder_MultipleDocsExport, data.ProductName + ".pdf");
                lock (_processLock)
                {
                    exportPath = GetPDFExportFilePath(ExistingFiles, Folder_MultipleDocsExport, data.ProductName);
                    ExistingFiles.Add(Path.GetFileName(exportPath));
                }
                var success = await _mulitplePDFDocumentExporter.ReadDataAndMulitplePDFDocumentAsync(data, exportPath);
                if (!success)
                {
                    failedCount++;
                    UpdateProgress("Exporting PDF", $"Processing: {index} of {toProcess.Count} | Failed: {failedCount}");
                }
                else
                {
                    successData.Add(data);
                }
            });

            //Additional tasks need to clear ToProcess and add use Success data.
        }



        //public void ExportSinglePDFDocument()
        //{
        //    _singlePDfDocumentExporter.ReadDataAndExportSinglePDFDocumentAsync(ImportDataFromExcel(), Folder_SingleDocsExport);
        //}

        //public void ExportMultiplePDFDocument()
        //{   
        //    //Todo           
        //    //Use the new Data model for downloaded images
        //    _mulitplePDFDocumentExporter.ReadDataAndMulitplePDFDocument(ImportDataFromExcel(), Folder_MultipleDocsExport);
        //}

        //public void DownloadImagesToRawFolder()
        //{
        //    if (isItOneDriveData)
        //    {
        //        if (!oneDriveAuthenticator.AuthenticateUserAndReturnAccessToken().ToString().IsNullOrEmpty())
        //        {
        //            _oneDriveImageDownloader.ReadDataAndDownloadImage(ImportDataFromExcel(), Folder_UncompressedImages, oneDriveAuthenticator.AuthenticateUserAndReturnAccessToken().ToString());
        //        }
        //    }
        //    else
        //    {
        //        _directLinkImageDownloader.ReadDataAndDownloadImage(ImportDataFromExcel(), Folder_UncompressedImages);
        //    }            
        //}

        private void Importer_ProgressUpdate(string message)
        {
            string function = "Importing";
            UpdateProgress(function, message);
        }

        private void UpdateProgress(string function, string message)
        {
            function ??= "";
            message ??= "";
            message = function + " | " + message;
            OnProgressUpdate?.Invoke(message);
        }


        #region Dispose
        private bool disposedValue;

        protected virtual void Dispose(bool disposing)
        {
            if (!disposedValue)
            {
                if (disposing)
                {
                    // TODO: dispose managed state (managed objects)
                }

                _spcErrorLog?.Dispose();
                _spcErrorLog = null;
                _textErrorLog?.Dispose();
                _textErrorLog = null;
                _importer = null;
                _workbook?.Dispose();
                _workbook = null;
                _imageDownloadManager?.Dispose();
                _imageDownloadManager = null;

                disposedValue = true;
            }
        }

        // // TODO: override finalizer only if 'Dispose(bool disposing)' has code to free unmanaged resources
        // ~B4UProcessor()
        // {
        //     // Do not change this code. Put cleanup code in 'Dispose(bool disposing)' method
        //     Dispose(disposing: false);
        // }

        public void Dispose()
        {
            // Do not change this code. Put cleanup code in 'Dispose(bool disposing)' method
            Dispose(disposing: true);
            GC.SuppressFinalize(this);
        }
        #endregion
    }
}
