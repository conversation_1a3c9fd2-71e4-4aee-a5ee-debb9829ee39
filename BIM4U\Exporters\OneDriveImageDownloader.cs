﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http.Headers;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using BIM4U.Data;
using Microsoft.IdentityModel.Tokens;

namespace BIM4U.Exporters
{
    public class OneDriveImageDownloader
    {

        List<ExcelData> exceldata;

        #region EventsAndDelegates
        public delegate void DirectLinkImageDownloaderErrorDelegate(string message);
        public event DirectLinkImageDownloaderErrorDelegate? OnError;
        #endregion

        public async void ReadDataAndDownloadImage(Task<List<ExcelData>> excelDatas, string ExportImagePath, string accesstoken)
        {
            try
            {
                exceldata = await excelDatas;
                for (int ia = 0; ia < exceldata.Count; ia++)
                {
                    CollectTheImageData(ia, exceldata[ia].ProductName, exceldata[ia].CompanyLogoUrl,
                        exceldata[ia].ProductImageUrl, exceldata[ia].ProductTechnicalImageUrl,
                        exceldata[ia].ProductImagesUrl, exceldata[ia].Colour1Image, exceldata[ia].Colour2Image, exceldata[ia].Colour3Image, ExportImagePath, accesstoken);
                }
            }
            catch (Exception ex)
            {
                OnError?.Invoke(ex.Message);
            }
        }

        private async void CollectTheImageData(int ID, string productname, string companyLogoUrl, string productImageUrl, string productTechnicalImage, List<string> productimagesUrl, string colour1Image, string colour2Image, string colour3Image, string filepath, string accessToken)
        {
          
            if (!companyLogoUrl.IsNullOrEmpty())
            {
                //To replace the image url
                exceldata[ID].CompanyLogoUrl = DownloadTheImage(productname + "-Company_Logo-", companyLogoUrl, filepath, accessToken).ToString();
            }
            else
            {
                string msg = string.Format("The Company Logo Url is null");
                throw new ArgumentNullException(msg);
            }

            if (!productImageUrl.IsNullOrEmpty())
            {
                //To replace the image url
                exceldata[ID].ProductImageUrl = DownloadTheImage(productname + "-Product_Image-", productImageUrl, filepath, accessToken).ToString();
            }
            else
            {
                string msg = string.Format("The product image Url is null");
                throw new ArgumentNullException(msg);
            }

            if (!productTechnicalImage.IsNullOrEmpty())
            {
                //To replace the image url
                exceldata[ID].ProductTechnicalImageUrl = DownloadTheImage(productname + "-Product_Technical_Image-", productTechnicalImage, filepath, accessToken).ToString();
            }
            else
            {
                string msg = string.Format("The product technical image Url is null");
                throw new ArgumentNullException(msg);
            }

            for (int i = 0; i >= productimagesUrl.Count - 1; i++)
            {
                if (!productimagesUrl[i].IsNullOrEmpty())
                {
                    //To replace the image url
                    exceldata[ID].ProductImagesUrl[i] = DownloadTheImage(productname + "-Product_Images_" + i, productimagesUrl[i], filepath, accessToken).ToString();
                }
                else
                {
                    string msg = string.Format("The product images " + i + " Url is null");
                    throw new ArgumentNullException(msg);
                }
            }

            // Process colour images for Available Colours section
            if (!colour1Image.IsNullOrEmpty())
            {
                exceldata[ID].Colour1Image = DownloadTheImage(productname + "-Colour1_Image-", colour1Image, filepath, accessToken).ToString();
            }

            if (!colour2Image.IsNullOrEmpty())
            {
                exceldata[ID].Colour2Image = DownloadTheImage(productname + "-Colour2_Image-", colour2Image, filepath, accessToken).ToString();
            }

            if (!colour3Image.IsNullOrEmpty())
            {
                exceldata[ID].Colour3Image = DownloadTheImage(productname + "-Colour3_Image-", colour3Image, filepath, accessToken).ToString();
            }
        }

        private static async System.Threading.Tasks.Task<string> DownloadTheImage(string productName, string fileUrl, string filepath, string accessToken)
        {
            try
            {
                using (var httpClient = new HttpClient())
                {
                    httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                    var response = await httpClient.GetAsync(fileUrl);

                    if (response.IsSuccessStatusCode)
                    {
                        string sharingUrl = fileUrl;

                        string encodedUrl = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(sharingUrl)).TrimEnd('=').Replace('/', '_').Replace('+', '-');
                        string requestUrl = $"https://graph.microsoft.com/v1.0/shares/u!{encodedUrl}/driveItem";

                        var httpClient1 = new HttpClient();
                        httpClient1.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
                        var response1 = await httpClient.GetAsync(requestUrl);

                        if (response1.IsSuccessStatusCode)
                        {
                            string responseBody = await response1.Content.ReadAsStringAsync();
                            dynamic driveItem = JsonConvert.DeserializeObject(responseBody);

                            string driveItemId = driveItem.id;
                            Console.WriteLine("Drive Item ID: " + driveItemId);

                            string fileEndpoint = $"https://graph.microsoft.com/v1.0/me/drive/items/{driveItemId}/content";

                            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
                            var fileResponse = await httpClient.GetAsync(fileEndpoint);

                            if (fileResponse.IsSuccessStatusCode)
                            {
                                // var fileContent = await fileResponse.Content.ReadAsStreamAsync();
                                // Save fileContent to a file or process it as needed
                                using (Stream responseStream = await fileResponse.Content.ReadAsStreamAsync(),
                                  fileStream2 = new FileStream(filepath + productName, FileMode.Create, FileAccess.Write, FileShare.None))
                                {
                                    await responseStream.CopyToAsync(fileStream2);
                                }
                            }

                            return filepath + productName;
                        }
                        else
                        {
                            string msg = string.Format("The was an error downloading the image");
                            throw new ArgumentNullException(msg);
                        }
                    }
                    else
                    {

                        string msg = string.Format($"There was an error downloading the image {response.StatusCode.ToString()}");
                        throw new ArgumentNullException(msg);
                    }
                }
            }
            catch (Exception ex)
            {
                string msg = string.Format($"There was an error downloading the image {ex.Message}");
                throw new ArgumentNullException(msg);
            }
        }
    }
}
