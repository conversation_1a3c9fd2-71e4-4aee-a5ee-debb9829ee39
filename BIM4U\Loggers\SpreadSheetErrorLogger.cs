﻿using DevExpress.Spreadsheet;
using DevExpress.XtraSpreadsheet;
using DocumentFormat.OpenXml.Drawing.Charts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIM4U.Loggers
{
    /// <summary>
    /// Logs errors from one spreadsheet to a new spreadsheet.
    /// </summary>
    public class SpreadSheetErrorLogger : IDisposable
    {
        private string _logFilePath;
        private bool disposedValue;
        Workbook _workbook;
        Worksheet _errorSheet;
        int _currentRowIndex;
        int _lastColIndex;
        private object _lock = new object();

        /// <summary>
        /// Creates a spreadsheet at the specified file path and copies error rows to it.
        /// </summary>
        /// <param name="logFilePath"></param>
        /// <exception cref="ArgumentNullException"></exception>
        public SpreadSheetErrorLogger(string logFilePath)
        {
            if (string.IsNullOrEmpty(logFilePath))
            {
                throw new ArgumentNullException(nameof(logFilePath));
            }

            if (!logFilePath.ToLower().EndsWith(".xlsx"))
            {
                logFilePath += ".xlsx";
            }
            _logFilePath = logFilePath;
        }

        public void LogError(Worksheet sourceSheet, int rowIndex, string errorMessage)
        {
            lock (_lock)
            {
                if (_workbook == null)
                {
                    InitializeSpreadsheet(sourceSheet);
                }

                //Copy error row to error spreadsheet
                _errorSheet.Rows[_currentRowIndex].CopyFrom(sourceSheet.Rows[rowIndex], PasteSpecial.All);
                //Add original row index and error message.
                _errorSheet[_currentRowIndex, _lastColIndex + 1].Value = rowIndex;
                _errorSheet[_currentRowIndex, _lastColIndex + 2].Value = errorMessage ?? "";
                //increment row index
                _currentRowIndex++;
            }
        }

        /// <summary>
        /// Creates a new spreadsheet control and copies column headers across.
        /// </summary>
        /// <param name="sourceSheet"></param>
        private void InitializeSpreadsheet(Worksheet sourceSheet)
        {
            lock (_lock)
            {
                if (_workbook == null)
                {
                    _workbook = new Workbook();
                    _workbook.CreateNewDocument();

                    var sheet = _workbook.Worksheets[0];

                    //Copy column headers from source sheet to the new sheet.
                    sheet.Rows[0].CopyFrom(sourceSheet.Rows[0], PasteSpecial.All);
                    _errorSheet = sheet;
                    //Update last used column index
                    _lastColIndex = sheet.GetDataRange().RightColumnIndex;
                    //Add column headers for original row index and error message
                    sheet[0, _lastColIndex + 1].Value = "Original Row Index";
                    sheet[0, _lastColIndex + 2].Value = "Error";

                    _currentRowIndex = 1;
                }
            }
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!disposedValue)
            {
                if (disposing)
                {
                    // TODO: dispose managed state (managed objects)
                }

                _workbook?.SaveDocument(_logFilePath);
                _workbook?.Dispose();
                _workbook = null;
                _errorSheet = null;
                disposedValue = true;
            }
        }

        public void Dispose()
        {
            // Do not change this code. Put cleanup code in 'Dispose(bool disposing)' method
            Dispose(disposing: true);
            GC.SuppressFinalize(this);
        }
    }
}
